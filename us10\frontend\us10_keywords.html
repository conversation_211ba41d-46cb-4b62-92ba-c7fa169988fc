<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keywords Analysis (US-05) - Dr. Resume</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div>
                <h1>🔍 Keywords Analysis (US-05)</h1>
                <nav class="dashboard-nav">
                    <a href="/dashboard" class="nav-item">📊 Dashboard</a>
                    <a href="/add-job-description" class="nav-item">📄 Add Job Description</a>
                    <a href="/upload" class="nav-item">📤 Upload Resume</a>
                    <a href="#" class="nav-item active">🔍 Keywords</a>
                    <a href="/matching" class="nav-item">🎯 Matching</a>
                    <a href="/suggestions" class="nav-item">💡 Suggestions</a>
                    <a href="/account" class="nav-item">⚙️ Account</a>
                </nav>
            </div>
            
            <div class="user-info">
                <span id="welcomeMessage">Welcome, GUEST</span>
                <button class="logout-btn" onclick="logout()">🔐 Login</button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <h2 style="margin-bottom: 30px; color: #1f2937;">Keyword Analysis & Extraction</h2>
            <p style="color: #6b7280; margin-bottom: 40px;">Extract and analyze keywords from your resumes and job descriptions using AI-powered NLP</p>
            
            <!-- Alert Messages -->
            <div id="alertContainer"></div>
            
            <!-- Keyword Statistics -->
            <div class="keyword-section">
                <h3>📊 Keyword Statistics</h3>
                <div class="keyword-stats" id="keywordStats">
                    <div class="keyword-stat">
                        <div class="keyword-stat-number" id="totalResumes">0</div>
                        <div class="keyword-stat-label">Total Resumes</div>
                    </div>
                    <div class="keyword-stat">
                        <div class="keyword-stat-number" id="processedResumes">0</div>
                        <div class="keyword-stat-label">Processed Resumes</div>
                    </div>
                    <div class="keyword-stat">
                        <div class="keyword-stat-number" id="totalJDs">0</div>
                        <div class="keyword-stat-label">Total Job Descriptions</div>
                    </div>
                    <div class="keyword-stat">
                        <div class="keyword-stat-number" id="processedJDs">0</div>
                        <div class="keyword-stat-label">Processed Job Descriptions</div>
                    </div>
                </div>
            </div>
            
            <!-- Bulk Processing -->
            <div class="parse-all-section">
                <h3 style="color: #1f2937; margin-bottom: 16px;">🚀 Bulk Keyword Extraction</h3>
                <p style="color: #6b7280; margin-bottom: 20px;">Process all your resumes and job descriptions at once to extract keywords automatically</p>
                
                <div class="parse-all-buttons">
                    <button class="parse-button" id="parseAllResumesBtn" onclick="parseAllResumes()">
                        📋 Parse All Resumes
                    </button>
                    <button class="parse-button" id="parseAllJDsBtn" onclick="parseAllJDs()">
                        📄 Parse All Job Descriptions
                    </button>
                </div>
            </div>
            
            <!-- Resume Keywords Section -->
            <div class="keyword-section">
                <h3>📋 Resume Keywords</h3>
                <div id="resumeKeywordsContainer">
                    <div class="keyword-empty">
                        Loading resume keywords...
                    </div>
                </div>
            </div>
            
            <!-- Job Description Keywords Section -->
            <div class="keyword-section">
                <h3>📄 Job Description Keywords</h3>
                <div id="jdKeywordsContainer">
                    <div class="keyword-empty">
                        Loading job description keywords...
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <div style="margin-top: 30px; text-align: center;">
                <a href="/dashboard" style="color: #7c3aed; text-decoration: none;">
                    ← Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <script src="/static/js/us10_keywords.js"></script>
</body>
</html>
