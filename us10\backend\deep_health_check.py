#!/usr/bin/env python3
"""
Deep Health Check Test for Dr. Resume Application
Comprehensive testing of all features and endpoints
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

class DrResumeHealthCheck:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.test_user_id = None
        self.test_resume_id = None
        self.test_jd_id = None
        self.results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }

    def log(self, message, status="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_emoji = {
            "INFO": "ℹ️",
            "PASS": "✅",
            "FAIL": "❌",
            "WARN": "⚠️"
        }
        print(f"[{timestamp}] {status_emoji.get(status, '📝')} {message}")

    def test_endpoint(self, method, endpoint, data=None, headers=None, expected_status=200, description=""):
        """Test a single endpoint"""
        try:
            url = f"{self.base_url}{endpoint}"
            
            if headers is None:
                headers = {}
            
            if self.auth_token and 'Authorization' not in headers:
                headers['Authorization'] = f'Bearer {self.auth_token}'
            
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers)
            elif method.upper() == 'POST':
                if data:
                    headers['Content-Type'] = 'application/json'
                    response = self.session.post(url, json=data, headers=headers)
                else:
                    response = self.session.post(url, headers=headers)
            elif method.upper() == 'PUT':
                headers['Content-Type'] = 'application/json'
                response = self.session.put(url, json=data, headers=headers)
            else:
                raise ValueError(f"Unsupported method: {method}")

            if response.status_code == expected_status:
                self.log(f"PASS: {description or endpoint}", "PASS")
                self.results['passed'] += 1
                return response.json() if response.content else {}
            else:
                self.log(f"FAIL: {description or endpoint} - Expected {expected_status}, got {response.status_code}", "FAIL")
                self.log(f"Response: {response.text[:200]}", "WARN")
                self.results['failed'] += 1
                self.results['errors'].append(f"{endpoint}: {response.status_code} - {response.text[:100]}")
                return None

        except Exception as e:
            self.log(f"ERROR: {description or endpoint} - {str(e)}", "FAIL")
            self.results['failed'] += 1
            self.results['errors'].append(f"{endpoint}: Exception - {str(e)}")
            return None

    def test_basic_health(self):
        """Test basic health endpoints"""
        self.log("Testing basic health endpoints...")
        
        # Test main health endpoint
        self.test_endpoint('GET', '/health', description="Main health check")
        
        # Test API health endpoint
        self.test_endpoint('GET', '/api/health', description="API health check")

    def test_authentication(self):
        """Test authentication system"""
        self.log("Testing authentication system...")
        
        # Test registration
        register_data = {
            "first_name": "Test",
            "last_name": "User",
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        result = self.test_endpoint('POST', '/api/register', register_data, description="User registration")
        
        if result and result.get('success'):
            self.test_user_id = result.get('user', {}).get('id')
        
        # Test login
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        result = self.test_endpoint('POST', '/api/login', login_data, description="User login")
        
        if result and result.get('success'):
            self.auth_token = result.get('access_token')
            self.log(f"Authentication token obtained: {self.auth_token[:20]}...", "PASS")

    def test_file_upload(self):
        """Test file upload functionality"""
        self.log("Testing file upload functionality...")
        
        if not self.auth_token:
            self.log("Skipping file upload test - no auth token", "WARN")
            return
        
        # Test resume upload endpoint (create test data)
        result = self.test_endpoint('POST', '/api/test-upload', description="Test resume upload")
        
        if result and result.get('success'):
            self.test_resume_id = result.get('resume', {}).get('id')

    def test_job_descriptions(self):
        """Test job description functionality"""
        self.log("Testing job description functionality...")
        
        if not self.auth_token:
            self.log("Skipping job description test - no auth token", "WARN")
            return
        
        # Test adding job description
        jd_data = {
            "title": "Senior Python Developer",
            "company_name": "Tech Corp",
            "job_text": "We are looking for a Senior Python Developer with experience in Flask, Django, and machine learning. Must have strong communication skills and experience with agile methodologies."
        }
        
        result = self.test_endpoint('POST', '/api/add_job_description', jd_data, description="Add job description")
        
        if result and result.get('success'):
            self.test_jd_id = result.get('job_description', {}).get('id')

    def test_keyword_extraction(self):
        """Test keyword extraction functionality"""
        self.log("Testing keyword extraction functionality...")
        
        if not self.auth_token:
            self.log("Skipping keyword extraction test - no auth token", "WARN")
            return
        
        # Test keyword extraction endpoints
        self.test_endpoint('GET', '/api/resumes', description="Get user resumes")
        self.test_endpoint('GET', '/api/job_descriptions', description="Get user job descriptions")

    def test_matching_algorithm(self):
        """Test matching algorithm functionality"""
        self.log("Testing matching algorithm functionality...")
        
        if not self.auth_token or not self.test_resume_id or not self.test_jd_id:
            self.log("Skipping matching test - missing prerequisites", "WARN")
            return
        
        # Test match calculation
        match_data = {
            "resume_id": self.test_resume_id,
            "job_description_id": self.test_jd_id
        }
        
        self.test_endpoint('POST', '/api/calculate_match', match_data, description="Calculate match score")
        
        # Test match history
        self.test_endpoint('GET', '/api/match_history', description="Get match history")

    def test_suggestions(self):
        """Test suggestions functionality"""
        self.log("Testing suggestions functionality...")
        
        if not self.auth_token:
            self.log("Skipping suggestions test - no auth token", "WARN")
            return
        
        # Test available suggestions data
        self.test_endpoint('GET', '/api/available_suggestions', description="Get available suggestions data")
        
        # Test basic suggestions (no auth required version)
        self.test_endpoint('GET', '/api/test-basic-suggestions', description="Test basic suggestions")
        
        # Test suggestions with auth
        if self.test_resume_id and self.test_jd_id:
            suggestions_data = {
                "resume_id": self.test_resume_id,
                "job_description_id": self.test_jd_id
            }
            self.test_endpoint('POST', '/api/basic_suggestions', suggestions_data, description="Generate basic suggestions")

    def test_account_management(self):
        """Test account management functionality"""
        self.log("Testing account management functionality...")
        
        if not self.auth_token:
            self.log("Skipping account management test - no auth token", "WARN")
            return
        
        # Test account info
        self.test_endpoint('GET', '/api/account', description="Get account information")
        
        # Test scan history (alias for match history)
        self.test_endpoint('GET', '/api/scan_history', description="Get scan history")

    def test_frontend_pages(self):
        """Test frontend page accessibility"""
        self.log("Testing frontend page accessibility...")
        
        pages = [
            ('/', 'Landing page'),
            ('/register', 'Registration page'),
            ('/login', 'Login page'),
            ('/dashboard', 'Dashboard page'),
            ('/upload', 'Upload page'),
            ('/job-descriptions', 'Job descriptions page'),
            ('/keywords', 'Keywords page'),
            ('/matching', 'Matching page'),
            ('/suggestions', 'Suggestions page'),
            ('/account', 'Account page')
        ]
        
        for endpoint, description in pages:
            # Frontend pages return HTML, so we expect 200 status
            response = self.session.get(f"{self.base_url}{endpoint}")
            if response.status_code == 200:
                self.log(f"PASS: {description}", "PASS")
                self.results['passed'] += 1
            else:
                self.log(f"FAIL: {description} - Status {response.status_code}", "FAIL")
                self.results['failed'] += 1

    def test_database_connectivity(self):
        """Test database connectivity"""
        self.log("Testing database connectivity...")
        
        # Test endpoints that require database access
        self.test_endpoint('GET', '/api/test-available-data', description="Test database connectivity")

    def run_all_tests(self):
        """Run all health check tests"""
        self.log("🩺 Starting Deep Health Check for Dr. Resume Application", "INFO")
        self.log("=" * 60, "INFO")
        
        start_time = time.time()
        
        # Run all test categories
        self.test_basic_health()
        self.test_database_connectivity()
        self.test_authentication()
        self.test_file_upload()
        self.test_job_descriptions()
        self.test_keyword_extraction()
        self.test_matching_algorithm()
        self.test_suggestions()
        self.test_account_management()
        self.test_frontend_pages()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Print summary
        self.log("=" * 60, "INFO")
        self.log("🏁 Health Check Summary", "INFO")
        self.log(f"✅ Tests Passed: {self.results['passed']}", "PASS")
        self.log(f"❌ Tests Failed: {self.results['failed']}", "FAIL")
        self.log(f"⏱️ Duration: {duration:.2f} seconds", "INFO")
        
        if self.results['errors']:
            self.log("🔍 Error Details:", "WARN")
            for error in self.results['errors']:
                self.log(f"  - {error}", "WARN")
        
        # Overall status
        if self.results['failed'] == 0:
            self.log("🎉 All tests passed! Application is healthy.", "PASS")
            return True
        else:
            self.log(f"⚠️ {self.results['failed']} tests failed. Please check the errors above.", "FAIL")
            return False

def main():
    """Main function to run health check"""
    print("🩺 Dr. Resume Deep Health Check")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly. Please start the application first.")
            print("Run: python app_fixed.py")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server at http://localhost:5000")
        print("Please start the application first with: python app_fixed.py")
        sys.exit(1)
    
    # Run health check
    health_check = DrResumeHealthCheck()
    success = health_check.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
