/**
 * US-07 Suggestions Service Frontend JavaScript
 * Handles suggestion generation, display, and management
 */

class SuggestionsManager {
    constructor() {
        this.token = localStorage.getItem('dr_resume_token');
        this.currentSuggestions = [];
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.loadAvailableData();
    }

    checkAuthentication() {
        if (!this.token) {
            this.showAlert('Please log in to access suggestions.', 'warning');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return false;
        }
        return true;
    }

    handleAuthError() {
        console.log('Authentication error - clearing tokens and redirecting');
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
        this.showAlert('Session expired. Please log in again.', 'warning');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
    }

    async makeAuthenticatedRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, mergedOptions);

            if (response.status === 401) {
                this.handleAuthError();
                return null;
            }

            return response;
        } catch (error) {
            console.error('Request error:', error);
            throw error;
        }
    }

    setupEventListeners() {
        // Generate suggestions buttons
        const generateBasicBtn = document.getElementById('generateBasicBtn');
        if (generateBasicBtn) {
            generateBasicBtn.addEventListener('click', () => {
                this.generateBasicSuggestions();
            });
        }

        const generatePremiumBtn = document.getElementById('generatePremiumBtn');
        if (generatePremiumBtn) {
            generatePremiumBtn.addEventListener('click', () => {
                this.generatePremiumSuggestions();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    async loadAvailableData() {
        try {
            const response = await this.makeAuthenticatedRequest('/api/available_suggestions');

            if (!response) {
                // Auth error handled by makeAuthenticatedRequest
                return;
            }

            if (response.ok) {
                const data = await response.json();
                console.log('Available data loaded:', data);

                if (data.success) {
                    this.populateSelects(data.resumes, data.job_descriptions);

                    // Show helpful messages if no data available
                    if (data.resumes.length === 0 && data.job_descriptions.length === 0) {
                        this.showAlert('No resumes or job descriptions found. Please upload a resume and add a job description first, then extract keywords.', 'info');
                    } else if (data.resumes.length === 0) {
                        this.showAlert('No resumes with extracted keywords found. Please upload a resume and extract keywords first.', 'info');
                    } else if (data.job_descriptions.length === 0) {
                        this.showAlert('No job descriptions with extracted keywords found. Please add a job description and extract keywords first.', 'info');
                    }
                } else {
                    throw new Error(data.message || 'Failed to load data');
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || 'Failed to load data');
            }
        } catch (error) {
            console.error('Error loading available data:', error);
            this.showAlert('Error loading resumes and job descriptions.', 'danger');
        }
    }

    populateSelects(resumes, jobDescriptions) {
        const resumeSelect = document.getElementById('resumeSelect');
        const jobSelect = document.getElementById('jobSelect');

        // Add null checks
        if (!resumeSelect || !jobSelect) {
            console.error('Resume or job select elements not found');
            return;
        }

        // Clear existing options
        resumeSelect.innerHTML = '<option value="">Select a resume...</option>';
        jobSelect.innerHTML = '<option value="">Select a job description...</option>';

        // Populate resumes
        resumes.forEach(resume => {
            const option = document.createElement('option');
            option.value = resume.id;
            option.textContent = resume.original_filename;
            resumeSelect.appendChild(option);
        });

        // Populate job descriptions
        jobDescriptions.forEach(jd => {
            const option = document.createElement('option');
            option.value = jd.id;
            option.textContent = `${jd.title} - ${jd.company_name || 'Unknown Company'}`;
            jobSelect.appendChild(option);
        });
    }

    async generateBasicSuggestions() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showAlert('Please select both a resume and job description.', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch('/api/basic_suggestions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId)
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.displaySuggestions(data.suggestions, 'basic');
                if (data.matching_score) {
                    this.displayMatchingScore(data.matching_score, 'basic');
                }
                this.showAlert(`Generated ${data.total_suggestions || data.suggestions?.length || 0} basic suggestions!`, 'success');
            } else {
                throw new Error(data.message || 'Failed to generate suggestions');
            }
        } catch (error) {
            console.error('Error generating basic suggestions:', error);
            this.showAlert('Error generating basic suggestions. Please try again.', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    async generatePremiumSuggestions() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showAlert('Please select both a resume and job description.', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch('/api/premium_suggestions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId)
                })
            });

            const data = await response.json();

            if (response.status === 402) {
                // Premium required
                this.showUpgradeModal();
                return;
            }

            if (response.ok) {
                this.displaySuggestions(data.suggestions, 'premium');
                if (data.matching_score) {
                    this.displayMatchingScore(data.matching_score, 'premium');
                }
                this.showAlert(`Generated ${data.total_suggestions || data.suggestions?.length || 0} premium suggestions!`, 'success');
            } else {
                throw new Error(data.message || 'Failed to generate premium suggestions');
            }
        } catch (error) {
            console.error('Error generating premium suggestions:', error);
            this.showAlert('Error generating premium suggestions. Please try again.', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displaySuggestions(suggestions, type) {
        const container = document.getElementById('suggestionsContainer');

        // Get the basic and premium cards directly from HTML
        const basicCard = document.getElementById('basicSuggestionsCard');
        const premiumCard = document.getElementById('premiumSuggestionsCard');

        // Add null checks for essential elements
        if (!container || !basicCard || !premiumCard) {
            console.error('Essential UI elements not found - container:', !!container, 'basicCard:', !!basicCard, 'premiumCard:', !!premiumCard);
            return;
        }

        console.log(`🔍 Displaying ${type} suggestions:`, suggestions);

        // TOGGLE BEHAVIOR: Show only the requested type, hide the other
        if (type === 'basic') {
            // Show ONLY basic suggestions, hide premium
            basicCard.style.display = 'block';
            premiumCard.style.display = 'none';

            // Update basic card count
            const basicCount = document.getElementById('basicCount');
            if (basicCount) {
                basicCount.textContent = suggestions.length;
            }

            // Display basic suggestions in categories
            this.displayBasicSuggestions(suggestions);

        } else if (type === 'premium') {
            // Show ONLY premium suggestions, hide basic
            basicCard.style.display = 'none';
            premiumCard.style.display = 'block';

            // Update premium card count
            const premiumCount = document.getElementById('premiumCount');
            if (premiumCount) {
                premiumCount.textContent = suggestions.length;
            }

            // Display premium suggestions in categories
            this.displayPremiumSuggestions(suggestions);
        }

        // Show the main container
        container.style.display = 'block';

        // Store suggestions
        this.currentSuggestions = suggestions;
        this.currentType = type;

        console.log(`✅ Toggle behavior applied: ${type} suggestions shown, other type hidden`);
    }

    displayBasicSuggestions(suggestions) {
        // Define basic categories that match the HTML structure
        const basicCategories = ['technicalSkills', 'softSkills', 'industryKeywords', 'atsOptimization', 'structure', 'quickWins'];

        // Group suggestions by category
        const groupedSuggestions = this.groupSuggestionsByCategory(suggestions, basicCategories);

        // Populate each basic category
        basicCategories.forEach(category => {
            const contentElement = document.getElementById(category + 'Content');
            if (contentElement) {
                const categorySuggestions = groupedSuggestions[category] || [];
                this.populateCategory(contentElement, categorySuggestions, category);
            } else {
                console.warn(`Basic category element not found: ${category}Content`);
            }
        });

        console.log(`✅ Basic suggestions populated in ${basicCategories.length} categories`);
    }

    displayPremiumSuggestions(suggestions) {
        // Define all premium categories that exist in the HTML template
        const premiumCategories = [
            { category: 'technicalSkills', elementId: 'technicalSkillsPremium' },
            { category: 'softSkills', elementId: 'softSkillsPremium' },
            { category: 'industryKeywords', elementId: 'industryKeywordsPremium' },
            { category: 'atsOptimization', elementId: 'atsOptimizationPremium' },
            { category: 'structure', elementId: 'structurePremium' },
            { category: 'quickWins', elementId: 'quickWinsPremium' },
            { category: 'criticalGaps', elementId: 'criticalGapsPremium' },
            { category: 'contextualAdvice', elementId: 'contextualAdvicePremium' },
            { category: 'quantification', elementId: 'quantificationPremium' },
            { category: 'skillDevelopment', elementId: 'skillDevelopmentPremium' }
        ];

        // Group suggestions by category - include all possible categories for grouping
        const allPossibleCategories = [
            'criticalGaps', 'contextualAdvice', 'quantification', 'skillDevelopment',
            'technicalSkills', 'softSkills', 'industryKeywords', 'atsOptimization', 'structure', 'quickWins'
        ];
        const groupedSuggestions = this.groupSuggestionsByCategory(suggestions, allPossibleCategories);

        console.log('🔍 Premium suggestions grouped:', groupedSuggestions);
        console.log('📊 Total premium suggestions received:', suggestions.length);

        // Populate premium categories that exist in HTML
        premiumCategories.forEach(({ category, elementId }) => {
            const categoryElement = document.getElementById(elementId);
            if (categoryElement) {
                const contentElement = categoryElement.querySelector('.category-content');
                const categorySuggestions = groupedSuggestions[category] || [];

                console.log(`📝 Populating ${category} (${elementId}):`, categorySuggestions.length, 'suggestions');

                if (categorySuggestions.length > 0) {
                    this.populateCategory(contentElement, categorySuggestions, category);
                } else {
                    // Show message if no suggestions for this category
                    contentElement.innerHTML = '<p style="color: #6b7280; font-style: italic;">No suggestions for this category yet</p>';
                }
            } else {
                console.warn(`❌ Premium category element not found: ${elementId}`);
            }
        });

        // Also try to populate any suggestions that don't fit the main 4 categories
        // by distributing them across the available categories
        const uncategorizedSuggestions = suggestions.filter(s =>
            !premiumCategories.some(pc => (s.category || '').toLowerCase().includes(pc.category.toLowerCase()))
        );

        if (uncategorizedSuggestions.length > 0) {
            console.log('📝 Distributing', uncategorizedSuggestions.length, 'uncategorized suggestions');

            // Distribute uncategorized suggestions to the first available category
            const firstCategory = premiumCategories[0];
            const firstElement = document.getElementById(firstCategory.elementId);
            if (firstElement) {
                const contentElement = firstElement.querySelector('.category-content');
                const existingSuggestions = groupedSuggestions[firstCategory.category] || [];
                const allSuggestions = [...existingSuggestions, ...uncategorizedSuggestions];
                this.populateCategory(contentElement, allSuggestions, firstCategory.category);
            }
        }

        console.log(`✅ Premium suggestions populated in ${premiumCategories.length} categories`);
    }

    createSimpleBasicCard(suggestion, index) {
        const card = document.createElement('div');
        card.className = 'basic-suggestion-card mb-3';

        const priority = suggestion.priority || 'medium';
        const priorityClass = priority.toLowerCase();

        card.innerHTML = `
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h6 class="card-title mb-0 fw-bold text-dark">${suggestion.title || suggestion.description}</h6>
                        <span class="badge bg-${this.getPriorityBootstrapColor(priority)} px-3 py-2">${priority.toUpperCase()}</span>
                    </div>

                    ${suggestion.description && suggestion.title !== suggestion.description ? `
                        <p class="card-text text-muted mb-3">${suggestion.description}</p>
                    ` : ''}

                    ${suggestion.action ? `
                        <div class="alert alert-light border-start border-primary border-3 mb-3">
                            <small class="text-primary fw-semibold">💡 Action Required:</small>
                            <div class="mt-1">${suggestion.action}</div>
                        </div>
                    ` : ''}

                    ${suggestion.keywords && suggestion.keywords.length > 0 ? `
                        <div class="mb-3">
                            <small class="text-muted fw-semibold d-block mb-2">🔑 Keywords:</small>
                            <div class="d-flex flex-wrap gap-1">
                                ${suggestion.keywords.map(keyword =>
                                    `<span class="badge bg-primary bg-opacity-10 text-primary">${keyword}</span>`
                                ).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <div class="d-flex justify-content-end">
                        <button class="btn btn-sm btn-success" onclick="suggestionsManager.markImplemented(${suggestion.id || 'null'})" ${suggestion.implemented ? 'disabled' : ''}>
                            <i class="fas fa-check me-1"></i>
                            ${suggestion.implemented ? 'Implemented' : 'Mark Done'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    getPriorityBootstrapColor(priority) {
        const colorMap = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'success'
        };
        return colorMap[priority.toLowerCase()] || 'secondary';
    }

    displayMatchingScore(matchingScore, type) {
        // Display enhanced matching score information with visual indicators
        if (!matchingScore) return;

        const scoreContent = document.getElementById('matchingScoreContent');
        if (!scoreContent) return;

        // Determine overall score color and message
        const overallScore = matchingScore.overall_score;
        let scoreColor, scoreMessage, recommendationText;

        if (overallScore >= 80) {
            scoreColor = 'success';
            scoreMessage = 'Excellent Match!';
            recommendationText = 'Your resume is very well-aligned with the job requirements. Focus on fine-tuning and formatting.';
        } else if (overallScore >= 60) {
            scoreColor = 'warning';
            scoreMessage = 'Good Match';
            recommendationText = 'Your resume covers most requirements but could benefit from adding missing keywords and skills.';
        } else if (overallScore >= 40) {
            scoreColor = 'info';
            scoreMessage = 'Moderate Match';
            recommendationText = 'Your resume has a solid foundation but needs enhancement in key areas to better match the job.';
        } else {
            scoreColor = 'danger';
            scoreMessage = 'Needs Improvement';
            recommendationText = 'Your resume has significant gaps. Focus on adding missing technical skills and relevant keywords.';
        }

        scoreContent.innerHTML = `
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="d-flex align-items-center mb-2">
                        <h4 class="text-${scoreColor} mb-0 me-3">${overallScore}%</h4>
                        <span class="badge bg-${scoreColor} fs-6">${scoreMessage}</span>
                    </div>
                    <p class="text-muted mb-0">${recommendationText}</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Technical Skills</span>
                            <span class="badge bg-${this.getScoreColor(matchingScore.technical_score)}">${matchingScore.technical_score}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${this.getScoreColor(matchingScore.technical_score)}"
                                 style="width: ${matchingScore.technical_score}%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Soft Skills</span>
                            <span class="badge bg-${this.getScoreColor(matchingScore.soft_skills_score)}">${matchingScore.soft_skills_score}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${this.getScoreColor(matchingScore.soft_skills_score)}"
                                 style="width: ${matchingScore.soft_skills_score}%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Industry Keywords</span>
                            <span class="badge bg-${this.getScoreColor(matchingScore.other_keywords_score)}">${matchingScore.other_keywords_score}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${this.getScoreColor(matchingScore.other_keywords_score)}"
                                 style="width: ${matchingScore.other_keywords_score}%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="score-item">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">Overall Match</span>
                            <span class="badge bg-${scoreColor}">${overallScore}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${scoreColor}"
                                 style="width: ${overallScore}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getScoreColor(score) {
        // Get Bootstrap color class based on score
        if (score >= 80) return 'success';
        if (score >= 60) return 'warning';
        if (score >= 40) return 'info';
        return 'danger';
    }

    groupSuggestionsByCategory(suggestions, categories) {
        const grouped = {};

        // Initialize all categories
        categories.forEach(category => {
            grouped[category] = [];
        });

        // Group suggestions by category or type
        suggestions.forEach(suggestion => {
            const category = this.mapSuggestionToCategory(suggestion);
            if (grouped[category]) {
                grouped[category].push(suggestion);
            } else {
                // Default to first category if mapping fails
                grouped[categories[0]].push(suggestion);
            }
        });

        return grouped;
    }

    mapSuggestionToCategory(suggestion) {
        // Map suggestion types to categories
        const typeMapping = {
            'technical_skills': 'technicalSkills',
            'soft_skills': 'softSkills',
            'industry_keywords': 'industryKeywords',
            'ats_optimization': 'atsOptimization',
            'structure': 'structure',
            'quick_wins': 'quickWins',
            'critical_gaps': 'criticalGaps',
            'contextual_advice': 'contextualAdvice',
            'quantification': 'quantification',
            'skill_development': 'skillDevelopment'
        };

        return typeMapping[suggestion.type] || typeMapping[suggestion.category] || 'technicalSkills';
    }

    populateCategory(contentElement, suggestions, categoryName) {
        // Add null check for contentElement
        if (!contentElement) {
            console.warn(`Content element not found for category: ${categoryName}`);
            return;
        }

        if (suggestions.length === 0) {
            contentElement.innerHTML = '<p class="text-muted">No suggestions yet</p>';
            return;
        }

        contentElement.innerHTML = '';
        suggestions.forEach(suggestion => {
            const suggestionElement = this.createSuggestionElement(suggestion, categoryName);
            if (suggestionElement) {
                contentElement.appendChild(suggestionElement);
            }
        });
    }

    updateCategoryCount(categoryElement, count) {
        const titleElement = categoryElement.querySelector('.category-title');

        // Remove existing count badge
        const existingBadge = titleElement.querySelector('.category-count');
        if (existingBadge) {
            existingBadge.remove();
        }

        // Add new count badge
        if (count > 0) {
            const countBadge = document.createElement('span');
            countBadge.className = 'category-count ms-2';
            countBadge.textContent = count;
            titleElement.appendChild(countBadge);
        }
    }

    createSuggestionElement(suggestion, categoryName) {
        const div = document.createElement('div');
        const priority = suggestion.priority || 'medium';
        const priorityClass = priority.toLowerCase();

        div.className = `suggestion-item ${priorityClass} ${suggestion.implemented ? 'implemented' : ''}`;

        // Determine if this is a premium category
        const premiumCategories = ['criticalGaps', 'contextualAdvice', 'quantification', 'skillDevelopment'];
        const isPremiumCategory = premiumCategories.includes(categoryName);

        div.innerHTML = `
            <div class="suggestion-header">
                <h4 class="suggestion-title">${suggestion.title || suggestion.description}</h4>
                <div class="d-flex align-items-center gap-2">
                    ${isPremiumCategory ? '<span class="badge bg-warning text-dark">Premium</span>' : ''}
                    <span class="suggestion-priority ${priorityClass}">${priority.toUpperCase()}</span>
                </div>
            </div>

            ${suggestion.description && suggestion.title !== suggestion.description ? `
                <p class="suggestion-description">${suggestion.description}</p>
            ` : ''}

            ${suggestion.action ? `
                <div class="suggestion-action">
                    <h6><i class="fas fa-lightbulb"></i> Action Required</h6>
                    <p>${suggestion.action}</p>
                </div>
            ` : ''}

            ${suggestion.keywords && suggestion.keywords.length > 0 ? `
                <div class="suggestion-keywords">
                    <span class="keywords-label">🔑 Keywords</span>
                    <div class="keywords-list">
                        ${suggestion.keywords.map(keyword =>
                            `<span class="keyword-tag">${keyword}</span>`
                        ).join('')}
                    </div>
                </div>
            ` : ''}

            ${suggestion.example ? `
                <div class="suggestion-example">
                    <h6><i class="fas fa-quote-left"></i> Example</h6>
                    <p>${suggestion.example}</p>
                </div>
            ` : ''}

            <div class="suggestion-actions mt-3">
                <button class="btn btn-sm btn-outline-success implement-btn"
                        onclick="suggestionsManager.markImplemented(${suggestion.id || 'null'})"
                        ${suggestion.implemented ? 'disabled' : ''}>
                    <i class="fas fa-check me-1"></i>
                    ${suggestion.implemented ? 'Implemented' : 'Mark Done'}
                </button>
            </div>
        `;

        return div;
    }

    getPriorityColor(priority) {
        const colorMap = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'success'
        };
        return colorMap[priority.toLowerCase()] || 'secondary';
    }

    async markImplemented(suggestionId) {
        if (!suggestionId) {
            this.showAlert('Cannot mark suggestion as implemented.', 'warning');
            return;
        }

        try {
            const response = await fetch(`/api/suggestions/${suggestionId}/implement`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    notes: 'Marked as implemented from frontend'
                })
            });

            if (response.ok) {
                this.showAlert('Suggestion marked as implemented!', 'success');
                // Refresh suggestions or update UI
                this.loadSuggestionHistory();
            } else {
                throw new Error('Failed to mark as implemented');
            }
        } catch (error) {
            console.error('Error marking suggestion as implemented:', error);
            this.showAlert('Error updating suggestion status.', 'danger');
        }
    }

    async loadSuggestionHistory() {
        try {
            const response = await fetch('/api/suggestions/history', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayHistory(data);
            }
        } catch (error) {
            console.error('Error loading suggestion history:', error);
        }
    }

    displayHistory(data) {
        const historyCard = document.getElementById('historyCard');
        const historyBody = document.getElementById('historyBody');

        if (data.analytics.total_suggestions > 0) {
            historyBody.innerHTML = `
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="analytics-stat">
                            <span class="stat-number">${data.analytics.total_suggestions}</span>
                            <span class="stat-label">Total Suggestions</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-stat">
                            <span class="stat-number">${data.analytics.implemented_suggestions}</span>
                            <span class="stat-label">Implemented</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-stat">
                            <span class="stat-number">${data.analytics.implementation_rate}%</span>
                            <span class="stat-label">Implementation Rate</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-stat">
                            <span class="stat-number">${data.analytics.premium_suggestions}</span>
                            <span class="stat-label">Premium Suggestions</span>
                        </div>
                    </div>
                </div>
            `;
            
            historyCard.style.display = 'block';
        }
    }

    showUpgradeModal() {
        const modal = new bootstrap.Modal(document.getElementById('upgradeModal'));
        modal.show();
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        if (show) {
            spinner.classList.remove('d-none');
        } else {
            spinner.classList.add('d-none');
        }
    }

    showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertDiv);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    logout() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.suggestionsManager = new SuggestionsManager();
});
