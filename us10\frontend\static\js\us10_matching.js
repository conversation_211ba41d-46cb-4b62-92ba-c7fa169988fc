/**
 * US-06: Matching Score JavaScript
 * Handles resume-job description matching functionality
 */

class MatchingManager {
    constructor() {
        this.token = localStorage.getItem('dr_resume_token');
        this.resumes = [];
        this.jobDescriptions = [];
        this.init();
    }

    init() {
        if (!this.token) {
            window.location.href = '/login';
            return;
        }

        this.setupEventListeners();
        this.loadResumes();
        this.loadJobDescriptions();
        this.loadMatchHistory();
    }

    setupEventListeners() {
        // Calculate match button
        document.getElementById('calculateBtn').addEventListener('click', () => {
            this.calculateMatch();
        });

        // Resume and job selection change
        document.getElementById('resumeSelect').addEventListener('change', () => {
            this.validateForm();
        });

        document.getElementById('jobSelect').addEventListener('change', () => {
            this.validateForm();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });
    }

    async loadResumes() {
        try {
            const response = await fetch('/api/resumes', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.resumes = data.resumes || [];
                this.populateResumeSelect();
            } else {
                console.error('Failed to load resumes');
                this.showError('Failed to load resumes');
            }
        } catch (error) {
            console.error('Error loading resumes:', error);
            this.showError('Error loading resumes');
        }
    }

    async loadJobDescriptions() {
        try {
            const response = await fetch('/api/job_descriptions', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.jobDescriptions = data.job_descriptions || [];
                this.populateJobSelect();
            } else {
                console.error('Failed to load job descriptions');
                this.showError('Failed to load job descriptions');
            }
        } catch (error) {
            console.error('Error loading job descriptions:', error);
            this.showError('Error loading job descriptions');
        }
    }

    populateResumeSelect() {
        const select = document.getElementById('resumeSelect');
        select.innerHTML = '<option value="">Select a resume...</option>';

        // Filter resumes with extracted keywords
        const validResumes = this.resumes.filter(resume => 
            resume.keywords_extracted && resume.upload_status === 'completed'
        );

        if (validResumes.length === 0) {
            select.innerHTML = '<option value="">No resumes with keywords available</option>';
            return;
        }

        validResumes.forEach(resume => {
            const option = document.createElement('option');
            option.value = resume.id;
            option.textContent = `${resume.original_filename} (${resume.keyword_count} keywords)`;
            select.appendChild(option);
        });

        this.validateForm();
    }

    populateJobSelect() {
        const select = document.getElementById('jobSelect');
        select.innerHTML = '<option value="">Select a job description...</option>';

        // Filter job descriptions with extracted keywords
        const validJobs = this.jobDescriptions.filter(jd => jd.keywords_extracted);

        if (validJobs.length === 0) {
            select.innerHTML = '<option value="">No job descriptions with keywords available</option>';
            return;
        }

        validJobs.forEach(jd => {
            const option = document.createElement('option');
            option.value = jd.id;
            option.textContent = `${jd.title} - ${jd.company_name || 'No Company'} (${jd.keyword_count} keywords)`;
            select.appendChild(option);
        });

        this.validateForm();
    }

    validateForm() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;
        const calculateBtn = document.getElementById('calculateBtn');

        calculateBtn.disabled = !resumeId || !jobId;
    }

    async calculateMatch() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showError('Please select both a resume and job description');
            return;
        }

        // Show loading
        this.showLoading(true);
        this.hideMatchResult();

        try {
            const response = await fetch('/api/calculate_match', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId)
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.displayMatchResult(data);
                this.showSuccess('Match score calculated successfully!');
                // Reload match history
                setTimeout(() => this.loadMatchHistory(), 1000);
            } else {
                this.showError(data.message || 'Failed to calculate match score');
            }
        } catch (error) {
            console.error('Error calculating match:', error);
            this.showError('Error calculating match score');
        } finally {
            this.showLoading(false);
        }
    }

    displayMatchResult(data) {
        const { match_score, detailed_scores, keyword_analysis } = data;

        // Update overall score
        document.getElementById('overallScore').textContent = `${detailed_scores.overall_score}%`;
        
        // Update detailed scores
        this.updateProgressBar('technicalProgress', 'technicalScore', detailed_scores.technical_score);
        this.updateProgressBar('softSkillsProgress', 'softSkillsScore', detailed_scores.soft_skills_score);
        this.updateProgressBar('otherProgress', 'otherScore', detailed_scores.other_keywords_score);

        // Update match details
        document.getElementById('matchedKeywords').textContent = keyword_analysis.matched_keywords;
        document.getElementById('totalResumeKeywords').textContent = keyword_analysis.total_resume_keywords;
        document.getElementById('totalJdKeywords').textContent = keyword_analysis.total_jd_keywords;
        document.getElementById('algorithmUsed').textContent = match_score.algorithm_used || 'Jaccard Similarity';

        // Update score circle color based on score
        this.updateScoreCircle(detailed_scores.overall_score);

        // Show result section
        document.getElementById('matchResult').style.display = 'block';
    }

    updateProgressBar(progressId, scoreId, score) {
        const progressBar = document.getElementById(progressId);
        const scoreElement = document.getElementById(scoreId);
        
        progressBar.style.width = `${score}%`;
        scoreElement.textContent = `${score}%`;
        
        // Add color based on score
        progressBar.className = 'progress-fill';
        if (score >= 80) {
            progressBar.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
        } else if (score >= 60) {
            progressBar.style.background = 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)';
        } else if (score >= 40) {
            progressBar.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
        } else {
            progressBar.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
        }
    }

    updateScoreCircle(score) {
        const circle = document.querySelector('.score-circle');
        const percentage = score / 100;
        const degrees = percentage * 360;
        
        let color1, color2;
        if (score >= 80) {
            color1 = '#10b981';
            color2 = '#059669';
        } else if (score >= 60) {
            color1 = '#3b82f6';
            color2 = '#2563eb';
        } else if (score >= 40) {
            color1 = '#f59e0b';
            color2 = '#d97706';
        } else {
            color1 = '#ef4444';
            color2 = '#dc2626';
        }
        
        circle.style.background = `conic-gradient(${color1} 0deg, ${color2} ${degrees}deg, #e5e7eb ${degrees}deg)`;
    }

    async loadMatchHistory() {
        try {
            const response = await fetch('/api/match_history?limit=5', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayMatchHistory(data.match_history || []);
            } else {
                console.error('Failed to load match history');
            }
        } catch (error) {
            console.error('Error loading match history:', error);
        }
    }

    displayMatchHistory(history) {
        const container = document.getElementById('historyContainer');
        
        if (history.length === 0) {
            container.innerHTML = '<div class="loading-text">No match history available</div>';
            return;
        }

        container.innerHTML = history.map(match => `
            <div class="history-item">
                <div class="history-header">
                    <div>
                        <div class="history-title">${match.resume_title || 'Resume'} vs ${match.job_title || 'Job'}</div>
                        <div class="history-meta">
                            ${match.company_name ? `${match.company_name} • ` : ''}
                            ${new Date(match.created_at).toLocaleDateString()}
                        </div>
                    </div>
                    <div class="history-score">
                        <div class="history-score-value ${this.getScoreCategory(match.overall_score)}">
                            ${match.overall_score}%
                        </div>
                        <div class="history-score-label">Match Score</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getScoreCategory(score) {
        if (score >= 80) return 'excellent';
        if (score >= 60) return 'good';
        if (score >= 40) return 'fair';
        return 'poor';
    }

    showLoading(show) {
        document.getElementById('loadingSection').style.display = show ? 'block' : 'none';
    }

    hideMatchResult() {
        document.getElementById('matchResult').style.display = 'none';
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        const successDiv = document.getElementById('successMessage');
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 3000);
    }

    logout() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MatchingManager();
});
