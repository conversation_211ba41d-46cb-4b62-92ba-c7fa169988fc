import os
from datetime import timedelta

# ============================================================================
# UNIFIED CONFIGURATION FOR DR.RESUME - US-01 TO US-10 INTEGRATION
# ============================================================================
# This file contains the complete configuration that supports all features
# from US-01 (Registration) through US-10 (Complete Integration)
# ============================================================================

class Config:
    """Base configuration class with all settings for US-01 to US-10"""
    
    # ========================================================================
    # CORE APPLICATION SETTINGS
    # ========================================================================
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dr-resume-secret-key-for-development'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # Base directories
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    SHARED_DIR = os.path.join(BASE_DIR, 'shared')
    
    # ========================================================================
    # DATABASE CONFIGURATION (US-01 Foundation)
    # ========================================================================
    
    DATABASE_DIR = os.path.join(SHARED_DIR, 'database')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'sqlite:///{os.path.join(DATABASE_DIR, "dr_resume_dev.db")}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # ========================================================================
    # JWT AUTHENTICATION CONFIGURATION (US-02)
    # ========================================================================
    
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-for-dr-resume'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'
    JWT_BLACKLIST_ENABLED = True
    JWT_BLACKLIST_TOKEN_CHECKS = ['access', 'refresh']
    
    # ========================================================================
    # FILE UPLOAD CONFIGURATION (US-03)
    # ========================================================================
    
    UPLOAD_FOLDER = os.path.join(SHARED_DIR, 'uploads')
    RESUME_UPLOAD_FOLDER = os.path.join(UPLOAD_FOLDER, 'resumes')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Allowed file extensions
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt'}
    
    # File processing settings
    FILE_PROCESSING_TIMEOUT = 30  # seconds
    
    # ========================================================================
    # NLP AND KEYWORD EXTRACTION CONFIGURATION (US-05)
    # ========================================================================
    
    # spaCy model configuration
    SPACY_MODEL = 'en_core_web_sm'
    NLTK_DATA_PATH = os.path.join(BASE_DIR, 'nltk_data')
    
    # Keyword extraction settings
    MIN_KEYWORD_LENGTH = 2
    MAX_KEYWORDS_PER_CATEGORY = 50
    KEYWORD_CONFIDENCE_THRESHOLD = 0.5
    
    # ========================================================================
    # MATCHING ALGORITHM CONFIGURATION (US-06)
    # ========================================================================
    
    # Scoring weights for different categories
    MATCHING_WEIGHTS = {
        'technical_skills': 0.5,    # 50% weight
        'soft_skills': 0.2,         # 20% weight
        'other_keywords': 0.3       # 30% weight
    }
    
    # Matching algorithm settings
    DEFAULT_MATCHING_ALGORITHM = 'jaccard'
    SIMILARITY_THRESHOLD = 0.1  # Minimum similarity to consider a match
    
    # ========================================================================
    # AI SUGGESTIONS CONFIGURATION (US-07)
    # ========================================================================
    
    # OpenAI configuration (for premium suggestions)
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    OPENAI_MODEL = 'gpt-3.5-turbo'
    OPENAI_MAX_TOKENS = 1000
    OPENAI_TEMPERATURE = 0.7
    
    # Suggestion generation settings
    MAX_BASIC_SUGGESTIONS = 10
    MAX_PREMIUM_SUGGESTIONS = 15
    SUGGESTION_CACHE_TIMEOUT = 3600  # 1 hour
    
    # ========================================================================
    # ANALYTICS AND DASHBOARD CONFIGURATION (US-08)
    # ========================================================================
    
    # Analytics settings
    ANALYTICS_RETENTION_DAYS = 365  # Keep analytics data for 1 year
    DASHBOARD_REFRESH_INTERVAL = 300  # 5 minutes
    
    # Chart and visualization settings
    DEFAULT_CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
    
    # ========================================================================
    # SECURITY AND API PROTECTION CONFIGURATION (US-09)
    # ========================================================================
    
    # Rate limiting settings
    RATE_LIMIT_SETTINGS = {
        'default': '100 per hour',
        'premium': '500 per hour',
        'admin': '1000 per hour'
    }
    
    # CORS settings
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*').split(',')
    
    # Security headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block'
    }
    
    # ========================================================================
    # LOGGING CONFIGURATION
    # ========================================================================
    
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.path.join(BASE_DIR, 'logs', 'dr_resume.log')
    
    # ========================================================================
    # EMAIL CONFIGURATION (Future enhancement)
    # ========================================================================
    
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # ========================================================================
    # ENVIRONMENT-SPECIFIC SETTINGS
    # ========================================================================
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.DATABASE_DIR, exist_ok=True)
        os.makedirs(Config.RESUME_UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(os.path.dirname(Config.LOG_FILE), exist_ok=True)


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Use PostgreSQL in production
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/dr_resume_prod'
    
    # Enhanced security in production
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    
    # Stricter rate limits in production
    RATE_LIMIT_SETTINGS = {
        'default': '50 per hour',
        'premium': '200 per hour',
        'admin': '500 per hour'
    }


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}


def get_config():
    """Get configuration based on environment"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])
