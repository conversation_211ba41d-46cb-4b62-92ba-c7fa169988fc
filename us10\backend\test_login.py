#!/usr/bin/env python3
"""
Quick test script to verify login endpoint is working
"""

import requests
import json

def test_login_endpoint():
    """Test the login endpoint"""
    url = "http://localhost:5000/api/login"
    
    # Test data
    test_data = {
        "email": "<EMAIL>",
        "password": "testpass"
    }
    
    try:
        response = requests.post(url, json=test_data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Login endpoint is working!")
        elif response.status_code == 401:
            print("✅ Login endpoint is working (user not found - expected)")
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_login_endpoint()
