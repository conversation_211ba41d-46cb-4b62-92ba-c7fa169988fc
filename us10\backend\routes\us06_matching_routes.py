"""
US-06: Matching Score Routes
API endpoints for calculating and retrieving resume-job description matching scores
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, User, Resume, JobDescription, MatchScore
from services.matching_service import MatchingService
from datetime import datetime

# Create blueprint for matching routes
matching_bp = Blueprint('matching', __name__, url_prefix='/api')

# Initialize matching service
matching_service = MatchingService()


@matching_bp.route('/calculate_match', methods=['POST'])
@jwt_required()
def calculate_match_score():
    """
    Calculate matching score between resume and job description
    Expected JSON payload:
    {
        "resume_id": 1,
        "job_description_id": 2
    }
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')
        
        if not resume_id or not job_description_id:
            return jsonify({
                'success': False,
                'message': 'Both resume_id and job_description_id are required'
            }), 400
        
        # Calculate match score
        result = matching_service.calculate_match_score(
            resume_id=resume_id,
            job_description_id=job_description_id,
            user_id=current_user_id
        )
        
        if not result['success']:
            return jsonify({
                'success': False,
                'message': 'Failed to calculate match score',
                'error': result.get('error')
            }), 400
        
        return jsonify({
            'success': True,
            'message': 'Match score calculated successfully',
            'match_score': result['match_score'],
            'detailed_scores': result['detailed_scores'],
            'keyword_analysis': result['keyword_analysis']
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in calculate_match_score: {e}")
        return jsonify({
            'success': False,
            'message': 'Error calculating match score',
            'error': str(e)
        }), 500


@matching_bp.route('/match_history', methods=['GET'])
@jwt_required()
def get_match_history():
    """
    Get user's match score history
    Query parameters:
    - limit: Number of results to return (default: 10)
    """
    try:
        current_user_id = get_jwt_identity()
        limit = request.args.get('limit', 10, type=int)
        
        # Validate limit
        if limit < 1 or limit > 100:
            limit = 10
        
        match_history = matching_service.get_match_history(
            user_id=current_user_id,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'message': f'Retrieved {len(match_history)} match scores',
            'match_history': match_history,
            'total_count': len(match_history)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in get_match_history: {e}")
        return jsonify({
            'success': False,
            'message': 'Error fetching match history',
            'error': str(e)
        }), 500


@matching_bp.route('/match_score/<int:match_id>', methods=['GET'])
@jwt_required()
def get_match_score_details(match_id):
    """
    Get detailed information about a specific match score
    """
    try:
        current_user_id = get_jwt_identity()
        
        result = matching_service.get_match_score(
            match_id=match_id,
            user_id=current_user_id
        )
        
        if not result['success']:
            return jsonify({
                'success': False,
                'message': result.get('error', 'Match score not found')
            }), 404
        
        return jsonify({
            'success': True,
            'match_score': result['match_score']
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in get_match_score_details: {e}")
        return jsonify({
            'success': False,
            'message': 'Error fetching match score details',
            'error': str(e)
        }), 500


@matching_bp.route('/bulk_calculate', methods=['POST'])
@jwt_required()
def bulk_calculate_matches():
    """
    Calculate match scores for multiple resume-job description pairs
    Expected JSON payload:
    {
        "pairs": [
            {"resume_id": 1, "job_description_id": 2},
            {"resume_id": 1, "job_description_id": 3}
        ]
    }
    """
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'pairs' not in data:
            return jsonify({
                'success': False,
                'message': 'Pairs array is required'
            }), 400
        
        pairs = data['pairs']
        if not isinstance(pairs, list) or len(pairs) == 0:
            return jsonify({
                'success': False,
                'message': 'Pairs must be a non-empty array'
            }), 400
        
        if len(pairs) > 20:  # Limit bulk operations
            return jsonify({
                'success': False,
                'message': 'Maximum 20 pairs allowed per bulk operation'
            }), 400
        
        results = []
        errors = []
        
        for i, pair in enumerate(pairs):
            try:
                resume_id = pair.get('resume_id')
                job_description_id = pair.get('job_description_id')
                
                if not resume_id or not job_description_id:
                    errors.append(f"Pair {i+1}: Missing resume_id or job_description_id")
                    continue
                
                result = matching_service.calculate_match_score(
                    resume_id=resume_id,
                    job_description_id=job_description_id,
                    user_id=current_user_id
                )
                
                if result['success']:
                    results.append({
                        'pair_index': i + 1,
                        'resume_id': resume_id,
                        'job_description_id': job_description_id,
                        'match_score': result['match_score'],
                        'overall_score': result['detailed_scores']['overall_score']
                    })
                else:
                    errors.append(f"Pair {i+1}: {result.get('error')}")
                    
            except Exception as e:
                errors.append(f"Pair {i+1}: {str(e)}")
        
        return jsonify({
            'success': True,
            'message': f'Processed {len(results)} pairs successfully',
            'results': results,
            'errors': errors if errors else None,
            'total_processed': len(results),
            'total_errors': len(errors)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in bulk_calculate_matches: {e}")
        return jsonify({
            'success': False,
            'message': 'Error in bulk calculation',
            'error': str(e)
        }), 500


@matching_bp.route('/resume/<int:resume_id>/matches', methods=['GET'])
@jwt_required()
def get_resume_matches(resume_id):
    """
    Get all match scores for a specific resume
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Verify resume belongs to user
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Get all match scores for this resume
        match_scores = MatchScore.query.filter_by(
            resume_id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).order_by(MatchScore.overall_score.desc()).all()
        
        return jsonify({
            'success': True,
            'resume_id': resume_id,
            'resume_filename': resume.original_filename,
            'match_scores': [score.to_dict(include_details=True) for score in match_scores],
            'total_matches': len(match_scores),
            'best_match': match_scores[0].to_dict(include_details=True) if match_scores else None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in get_resume_matches: {e}")
        return jsonify({
            'success': False,
            'message': 'Error fetching resume matches',
            'error': str(e)
        }), 500


@matching_bp.route('/job_description/<int:jd_id>/matches', methods=['GET'])
@jwt_required()
def get_job_description_matches(jd_id):
    """
    Get all match scores for a specific job description
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Verify job description belongs to user
        jd = JobDescription.query.filter_by(
            id=jd_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not jd:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Get all match scores for this job description
        match_scores = MatchScore.query.filter_by(
            job_description_id=jd_id,
            user_id=current_user_id,
            is_active=True
        ).order_by(MatchScore.overall_score.desc()).all()
        
        return jsonify({
            'success': True,
            'job_description_id': jd_id,
            'job_title': jd.title,
            'company_name': jd.company_name,
            'match_scores': [score.to_dict(include_details=True) for score in match_scores],
            'total_matches': len(match_scores),
            'best_match': match_scores[0].to_dict(include_details=True) if match_scores else None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in get_job_description_matches: {e}")
        return jsonify({
            'success': False,
            'message': 'Error fetching job description matches',
            'error': str(e)
        }), 500
