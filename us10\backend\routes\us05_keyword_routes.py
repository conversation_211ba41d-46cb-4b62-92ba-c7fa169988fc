from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, User, Resume, JobDescription
from services.keyword_parser import KeywordParser
from datetime import datetime

# Create blueprint for keyword parsing routes
keyword_bp = Blueprint('keywords', __name__, url_prefix='/api')

# Initialize keyword parser
keyword_parser = KeywordParser()

@keyword_bp.route('/parse_resume_keywords/<int:resume_id>', methods=['POST'])
@jwt_required()
def parse_resume_keywords(resume_id):
    """
    Parse keywords from a specific resume
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get the resume
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        if not resume.extracted_text:
            return jsonify({
                'success': False,
                'message': 'No text available for keyword extraction. Please ensure the resume was processed successfully.'
            }), 400
        
        # Extract keywords
        keywords = keyword_parser.extract_keywords(resume.extracted_text)
        
        # Save keywords to database
        resume.set_keywords(
            technical_skills=keywords['technical_skills'],
            soft_skills=keywords['soft_skills'],
            other_keywords=keywords['other_keywords']
        )
        resume.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Keywords extracted successfully',
            'resume': resume.to_dict(include_keywords=True),
            'keywords': keywords
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Error extracting keywords',
            'error': str(e)
        }), 500

@keyword_bp.route('/parse_jd_keywords/<int:jd_id>', methods=['POST'])
@jwt_required()
def parse_jd_keywords(jd_id):
    """
    Parse keywords from a specific job description
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get the job description
        jd = JobDescription.query.filter_by(
            id=jd_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not jd:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        if not jd.job_text:
            return jsonify({
                'success': False,
                'message': 'No text available for keyword extraction.'
            }), 400
        
        # Extract keywords
        keywords = keyword_parser.extract_keywords(jd.job_text)
        
        # Save keywords to database
        jd.set_keywords(
            technical_skills=keywords['technical_skills'],
            soft_skills=keywords['soft_skills'],
            other_keywords=keywords['other_keywords']
        )
        jd.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Keywords extracted successfully',
            'job_description': jd.to_dict(include_keywords=True),
            'keywords': keywords
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Error extracting keywords',
            'error': str(e)
        }), 500

@keyword_bp.route('/parse_all_resume_keywords', methods=['POST'])
@jwt_required()
def parse_all_resume_keywords():
    """
    Parse keywords from all user's resumes that haven't been processed
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get all resumes without keyword extraction
        resumes = Resume.query.filter_by(
            user_id=current_user_id,
            is_active=True,
            keywords_extracted=False
        ).filter(Resume.extracted_text.isnot(None)).all()
        
        if not resumes:
            return jsonify({
                'success': True,
                'message': 'No resumes found that need keyword extraction',
                'processed_count': 0
            }), 200
        
        processed_count = 0
        errors = []
        
        for resume in resumes:
            try:
                # Extract keywords
                keywords = keyword_parser.extract_keywords(resume.extracted_text)
                
                # Save keywords to database
                resume.set_keywords(
                    technical_skills=keywords['technical_skills'],
                    soft_skills=keywords['soft_skills'],
                    other_keywords=keywords['other_keywords']
                )
                resume.updated_at = datetime.utcnow()
                
                processed_count += 1
                
            except Exception as e:
                errors.append(f"Resume {resume.id}: {str(e)}")
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Processed {processed_count} resumes successfully',
            'processed_count': processed_count,
            'errors': errors if errors else None
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Error processing resumes',
            'error': str(e)
        }), 500

@keyword_bp.route('/parse_all_jd_keywords', methods=['POST'])
@jwt_required()
def parse_all_jd_keywords():
    """
    Parse keywords from all user's job descriptions that haven't been processed
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Get all job descriptions without keyword extraction
        jds = JobDescription.query.filter_by(
            user_id=current_user_id,
            is_active=True,
            keywords_extracted=False
        ).all()
        
        if not jds:
            return jsonify({
                'success': True,
                'message': 'No job descriptions found that need keyword extraction',
                'processed_count': 0
            }), 200
        
        processed_count = 0
        errors = []
        
        for jd in jds:
            try:
                # Extract keywords
                keywords = keyword_parser.extract_keywords(jd.job_text)
                
                # Save keywords to database
                jd.set_keywords(
                    technical_skills=keywords['technical_skills'],
                    soft_skills=keywords['soft_skills'],
                    other_keywords=keywords['other_keywords']
                )
                jd.updated_at = datetime.utcnow()
                
                processed_count += 1
                
            except Exception as e:
                errors.append(f"Job Description {jd.id}: {str(e)}")
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Processed {processed_count} job descriptions successfully',
            'processed_count': processed_count,
            'errors': errors if errors else None
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Error processing job descriptions',
            'error': str(e)
        }), 500

@keyword_bp.route('/keywords/resume/<int:resume_id>', methods=['GET'])
@jwt_required()
def get_resume_keywords(resume_id):
    """
    Get extracted keywords for a specific resume
    """
    try:
        current_user_id = get_jwt_identity()
        
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        return jsonify({
            'success': True,
            'resume': resume.to_dict(include_keywords=True),
            'keywords_extracted': resume.keywords_extracted
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Error fetching resume keywords',
            'error': str(e)
        }), 500

@keyword_bp.route('/keywords/job_description/<int:jd_id>', methods=['GET'])
@jwt_required()
def get_jd_keywords(jd_id):
    """
    Get extracted keywords for a specific job description
    """
    try:
        current_user_id = get_jwt_identity()
        
        jd = JobDescription.query.filter_by(
            id=jd_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not jd:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        return jsonify({
            'success': True,
            'job_description': jd.to_dict(include_keywords=True),
            'keywords_extracted': jd.keywords_extracted
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Error fetching job description keywords',
            'error': str(e)
        }), 500

@keyword_bp.route('/keywords/stats', methods=['GET'])
@jwt_required()
def get_keyword_stats():
    """
    Get keyword extraction statistics for the user
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Count resumes
        total_resumes = Resume.query.filter_by(
            user_id=current_user_id,
            is_active=True
        ).count()
        
        processed_resumes = Resume.query.filter_by(
            user_id=current_user_id,
            is_active=True,
            keywords_extracted=True
        ).count()
        
        # Count job descriptions
        total_jds = JobDescription.query.filter_by(
            user_id=current_user_id,
            is_active=True
        ).count()
        
        processed_jds = JobDescription.query.filter_by(
            user_id=current_user_id,
            is_active=True,
            keywords_extracted=True
        ).count()
        
        return jsonify({
            'success': True,
            'stats': {
                'resumes': {
                    'total': total_resumes,
                    'processed': processed_resumes,
                    'pending': total_resumes - processed_resumes
                },
                'job_descriptions': {
                    'total': total_jds,
                    'processed': processed_jds,
                    'pending': total_jds - processed_jds
                }
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Error fetching keyword stats',
            'error': str(e)
        }), 500
