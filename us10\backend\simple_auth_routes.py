#!/usr/bin/env python3
"""
Simple Auth Routes for Dr. <PERSON>
Provides basic authentication endpoints for testing
"""

from flask import Blueprint, request, jsonify
from models import db, User
from flask_jwt_extended import create_access_token

# Create blueprint for simple authentication routes
simple_auth_bp = Blueprint('simple_auth', __name__, url_prefix='/api')

@simple_auth_bp.route('/simple-register', methods=['POST'])
def simple_register():
    """Simple registration endpoint for testing"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        # Basic validation
        if not all([first_name, last_name, email, password]):
            return jsonify({
                'success': False,
                'message': 'All fields are required'
            }), 400
        
        # Check if user already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            return jsonify({
                'success': False,
                'message': 'User already exists'
            }), 409
        
        # Create new user
        user = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            password=password
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': {
                'id': user.id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Registration failed: {str(e)}'
        }), 500

@simple_auth_bp.route('/simple-login', methods=['POST'])
def simple_login():
    """Simple login endpoint for testing"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        # Find user
        user = User.query.filter_by(email=email).first()
        
        if not user or not user.check_password(password):
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401
        
        # Generate access token
        access_token = create_access_token(identity=user.id)
        
        # Update last login
        user.update_last_login()
        
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'access_token': access_token,
            'user': {
                'id': user.id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Login failed: {str(e)}'
        }), 500
