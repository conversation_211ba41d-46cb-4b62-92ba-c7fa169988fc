# 🔧 Backend Deep Dive - Dr. Resume Integration

## 📋 **Table of Contents**
1. [Architecture Overview](#architecture-overview)
2. [Application Factory](#application-factory)
3. [Database Models](#database-models)
4. [Services Layer](#services-layer)
5. [Routes & API](#routes--api)
6. [Middleware & Security](#middleware--security)
7. [Integration Patterns](#integration-patterns)

---

## 🏗️ **Architecture Overview**

The Dr. Resume backend follows a **layered architecture** pattern that evolved through US1-US10:

```
┌─────────────────────────────────────────┐
│           Flask Application             │
├─────────────────────────────────────────┤
│              Middleware                 │
│        (Auth, Monitoring, CORS)         │
├─────────────────────────────────────────┤
│               Routes                    │
│         (API Endpoints)                 │
├─────────────────────────────────────────┤
│              Services                   │
│         (Business Logic)                │
├─────────────────────────────────────────┤
│               Models                    │
│         (Database Layer)                │
├─────────────────────────────────────────┤
│              Database                   │
│            (SQLite)                     │
└─────────────────────────────────────────┘
```

### **Key Design Principles**
- **Separation of Concerns**: Each layer has specific responsibilities
- **Dependency Injection**: Services are injected into routes
- **Error Handling**: Comprehensive try-catch with logging
- **Security First**: JWT authentication, input validation
- **Scalability**: Modular design for easy feature addition

---

## 🏭 **Application Factory**

### **File**: `us10/backend/app_fixed.py`

The application factory pattern creates and configures the Flask app:

```python
def create_app():
    """Create and configure the Flask application"""
    
    # 1. Create Flask instance
    app = Flask(__name__, 
                template_folder='../frontend',
                static_folder='../frontend/static')
    
    # 2. Configure paths and settings
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))
    db_path = os.path.join(project_root, 'shared', 'database', 'dr_resume_dev.db')
    
    # 3. Set configuration
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['JWT_SECRET_KEY'] = 'jwt-secret-key-change-in-production'
    
    # 4. Initialize extensions
    db.init_app(app)
    CORS(app)
    JWTManager(app)
    
    # 5. Register blueprints (routes)
    register_routes(app)
    
    # 6. Create database tables
    with app.app_context():
        db.create_all()
    
    return app
```

### **Configuration Management**
- **Database**: SQLite in shared folder for cross-US compatibility
- **Security**: JWT tokens, CORS enabled
- **File Uploads**: Shared uploads directory
- **Logging**: Comprehensive error tracking

---

## 🗄️ **Database Models**

### **File**: `us10/backend/models.py`

The database schema evolved through each US, creating a comprehensive data model:

### **User Model** (US1-US2)
```python
class User(db.Model):
    __tablename__ = 'users'
    
    # Core fields
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Profile information
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    
    # Account status
    is_active = db.Column(db.Boolean, default=True)
    is_premium = db.Column(db.Boolean, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    resumes = db.relationship('Resume', backref='user', lazy=True)
    job_descriptions = db.relationship('JobDescription', backref='user', lazy=True)
```

### **Resume Model** (US3 + US5)
```python
class Resume(db.Model):
    __tablename__ = 'resumes'
    
    # Core fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # File information (US3)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    extracted_text = db.Column(db.Text)
    
    # Keyword analysis (US5)
    keywords_extracted = db.Column(db.Boolean, default=False)
    technical_skills = db.Column(db.Text)  # JSON string
    soft_skills = db.Column(db.Text)       # JSON string
    other_keywords = db.Column(db.Text)    # JSON string
    keyword_count = db.Column(db.Integer, default=0)
```

### **JobDescription Model** (US4 + US5)
```python
class JobDescription(db.Model):
    __tablename__ = 'job_descriptions'
    
    # Core fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Job information (US4)
    title = db.Column(db.String(255), nullable=False)
    company_name = db.Column(db.String(255))
    job_text = db.Column(db.Text, nullable=False)
    
    # Keyword analysis (US5)
    keywords_extracted = db.Column(db.Boolean, default=False)
    technical_skills = db.Column(db.Text)
    soft_skills = db.Column(db.Text)
    other_keywords = db.Column(db.Text)
```

### **MatchScore Model** (US6)
```python
class MatchScore(db.Model):
    __tablename__ = 'match_scores'
    
    # Relationship fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    resume_id = db.Column(db.Integer, db.ForeignKey('resumes.id'), nullable=False)
    job_description_id = db.Column(db.Integer, db.ForeignKey('job_descriptions.id'))
    
    # Score fields
    overall_score = db.Column(db.Float, nullable=False)
    technical_score = db.Column(db.Float, default=0.0)
    soft_skills_score = db.Column(db.Float, default=0.0)
    other_keywords_score = db.Column(db.Float, default=0.0)
    
    # Metadata
    algorithm_used = db.Column(db.String(50), default='jaccard')
    total_resume_keywords = db.Column(db.Integer, default=0)
    total_jd_keywords = db.Column(db.Integer, default=0)
    matched_keywords = db.Column(db.Integer, default=0)
```

### **Suggestion Model** (US7)
```python
class Suggestion(db.Model):
    __tablename__ = 'suggestions'
    
    # Relationship fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    resume_id = db.Column(db.Integer, db.ForeignKey('resumes.id'), nullable=False)
    job_description_id = db.Column(db.Integer, db.ForeignKey('job_descriptions.id'))
    
    # Suggestion content
    suggestion_type = db.Column(db.String(50), nullable=False)  # 'basic' or 'premium'
    category = db.Column(db.String(100), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    priority = db.Column(db.String(20), default='medium')
    
    # Implementation details
    keywords = db.Column(db.Text)  # JSON array
    action = db.Column(db.Text)
    placement = db.Column(db.Text)  # JSON array
    example = db.Column(db.Text)
```

---

## ⚙️ **Services Layer**

The services layer contains the core business logic, separated from API routes:

### **File Parser Service** (US3)
**File**: `us10/backend/services/file_parser.py`

```python
class FileParser:
    """Extract text from PDF and DOCX files"""
    
    def parse_file(self, file_path: str, file_type: str) -> Dict:
        """Extract text from uploaded file"""
        try:
            if file_type.lower() == 'pdf':
                return self._parse_pdf(file_path)
            elif file_type.lower() in ['docx', 'doc']:
                return self._parse_docx(file_path)
            else:
                return {'success': False, 'error': 'Unsupported file type'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _parse_pdf(self, file_path: str) -> Dict:
        """Extract text from PDF using PyPDF2"""
        # Implementation details...
    
    def _parse_docx(self, file_path: str) -> Dict:
        """Extract text from DOCX using python-docx"""
        # Implementation details...
```

### **Keyword Parser Service** (US5)
**File**: `us10/backend/services/keyword_parser.py`

```python
class KeywordParser:
    """Extract and categorize keywords using NLP"""
    
    def extract_keywords(self, text: str) -> Dict:
        """Extract keywords from text using spaCy NLP"""
        try:
            # Load spaCy model
            nlp = spacy.load('en_core_web_sm')
            doc = nlp(text)
            
            # Extract different types of keywords
            technical_skills = self._extract_technical_skills(doc)
            soft_skills = self._extract_soft_skills(doc)
            other_keywords = self._extract_other_keywords(doc)
            
            return {
                'success': True,
                'technical_skills': technical_skills,
                'soft_skills': soft_skills,
                'other_keywords': other_keywords,
                'total_count': len(technical_skills) + len(soft_skills) + len(other_keywords)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
```

### **Matching Service** (US6)
**File**: `us10/backend/services/matching_service.py`

```python
class MatchingService:
    """Calculate compatibility between resume and job description"""
    
    def calculate_match_score(self, resume_id: int, job_description_id: int, user_id: int) -> Dict:
        """Calculate comprehensive matching score using Jaccard similarity"""
        try:
            # Get resume and JD data
            resume = Resume.query.get(resume_id)
            jd = JobDescription.query.get(job_description_id)
            
            # Extract keywords
            resume_keywords = self._get_keywords(resume)
            jd_keywords = self._get_keywords(jd)
            
            # Calculate Jaccard similarity for each category
            technical_score = self._calculate_jaccard_similarity(
                resume_keywords['technical'], jd_keywords['technical']
            )
            soft_skills_score = self._calculate_jaccard_similarity(
                resume_keywords['soft_skills'], jd_keywords['soft_skills']
            )
            
            # Calculate overall score
            overall_score = (technical_score * 0.6 + soft_skills_score * 0.4) * 100
            
            # Save to database
            match_score = self._save_match_score(
                user_id, resume_id, job_description_id, 
                overall_score, technical_score, soft_skills_score
            )
            
            return {
                'success': True,
                'match_score': match_score.to_dict(),
                'detailed_scores': {
                    'overall_score': overall_score,
                    'technical_score': technical_score * 100,
                    'soft_skills_score': soft_skills_score * 100
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _calculate_jaccard_similarity(self, set1: List[str], set2: List[str]) -> float:
        """Calculate Jaccard similarity: |A ∩ B| / |A ∪ B|"""
        set1 = set(set1)
        set2 = set(set2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0.0
```

---

## 🛣️ **Routes & API**

The API layer provides RESTful endpoints organized by feature:

### **Authentication Routes** (US1-US2)
**File**: `us10/backend/routes/us05_auth_routes.py`

```python
@auth_bp.route('/register', methods=['POST'])
@public_route
@monitored_route
def register():
    """Register a new user"""
    try:
        data = request.get_json()

        # Validation
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()

        # Check if user exists
        if User.query.filter_by(email=email).first():
            return jsonify({'success': False, 'message': 'Email already registered'}), 400

        # Create new user
        user = User(email=email, first_name=first_name, last_name=last_name)
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Registration successful',
            'user': user.to_dict()
        }), 201

    except Exception as e:
        return jsonify({'success': False, 'message': 'Registration failed'}), 500

@auth_bp.route('/login', methods=['POST'])
@public_route
@monitored_route
def login():
    """Login user and return JWT tokens"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')

        user = User.query.filter_by(email=email).first()

        if not user or not user.check_password(password):
            return jsonify({'success': False, 'message': 'Invalid credentials'}), 401

        # Generate JWT tokens
        tokens = user.generate_tokens()
        user.update_last_login()

        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': user.to_dict(),
            'tokens': tokens
        }), 200

    except Exception as e:
        return jsonify({'success': False, 'message': 'Login failed'}), 500
```

### **Upload Routes** (US3)
**File**: `us10/backend/routes/us05_upload_routes.py`

```python
@upload_bp.route('/upload_resume', methods=['POST'])
@protected_route
@monitored_route
def upload_resume():
    """Upload and parse resume file"""
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        # Check file upload
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'pdf', 'docx', 'doc'}
        file_extension = file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'success': False, 'message': 'Invalid file type'}), 400

        # Save file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        # Parse file content
        parser = FileParser()
        parse_result = parser.parse_file(file_path, file_extension)

        if not parse_result['success']:
            return jsonify({'success': False, 'message': 'File parsing failed'}), 500

        # Create resume record
        resume = Resume(
            user_id=user.id,
            original_filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path),
            file_type=file_extension,
            extracted_text=parse_result['text']
        )

        db.session.add(resume)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Resume uploaded successfully',
            'resume': resume.to_dict()
        }), 201

    except Exception as e:
        return jsonify({'success': False, 'message': 'Upload failed'}), 500
```

### **Suggestions Routes** (US7)
**File**: `us10/backend/routes/us07_suggestions_routes.py`

```python
@suggestions_bp.route('/basic_suggestions', methods=['POST'])
@protected_route
@monitored_route
def generate_basic_suggestions():
    """Generate basic AI suggestions"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        data = request.get_json()
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')

        # Validate ownership
        resume = Resume.query.filter_by(id=resume_id, user_id=user.id).first()
        jd = JobDescription.query.filter_by(id=job_description_id, user_id=user.id).first()

        if not resume or not jd:
            return jsonify({'success': False, 'message': 'Invalid resume or job description'}), 404

        # Generate suggestions
        suggestions_service = DynamicSuggestionsService()
        result = suggestions_service.generate_basic_suggestions(
            resume_id=resume_id,
            job_description_id=job_description_id,
            user_id=user.id
        )

        if not result['success']:
            return jsonify({'success': False, 'message': result['message']}), 500

        return jsonify({
            'success': True,
            'message': 'Basic suggestions generated successfully',
            'suggestions': result['suggestions'],
            'total_suggestions': result['total_suggestions'],
            'matching_score': result['matching_score']
        }), 200

    except Exception as e:
        return jsonify({'success': False, 'message': 'Suggestion generation failed'}), 500
```

---

## 🛡️ **Middleware & Security**

### **Authentication Middleware**
**File**: `us10/backend/middleware/auth_middleware.py`

```python
def protected_route(f):
    """Decorator for routes requiring authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Verify JWT token
            verify_jwt_in_request()
            return f(*args, **kwargs)
        except Exception as e:
            return jsonify({
                'success': False,
                'message': 'Authentication required',
                'error': 'auth_failed'
            }), 401
    return decorated_function

def monitored_route(f):
    """Decorator for logging and monitoring"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()

        try:
            result = f(*args, **kwargs)
            duration = time.time() - start_time

            # Log successful request
            logger.info(f"Route {request.endpoint} completed in {duration:.2f}s")
            return result

        except Exception as e:
            duration = time.time() - start_time

            # Log error
            logger.error(f"Route {request.endpoint} failed after {duration:.2f}s: {str(e)}")
            raise

    return decorated_function
```

---

## 🔗 **Integration Patterns**

### **Service Injection Pattern**
```python
# In routes, services are instantiated and used
suggestions_service = DynamicSuggestionsService()
result = suggestions_service.generate_basic_suggestions(...)
```

### **Error Handling Pattern**
```python
try:
    # Business logic
    result = service.do_something()

    if not result['success']:
        return jsonify({'success': False, 'message': result['message']}), 400

    return jsonify({'success': True, 'data': result['data']}), 200

except Exception as e:
    logger.error(f"Error in {request.endpoint}: {e}")
    return jsonify({'success': False, 'message': 'Internal server error'}), 500
```

### **Database Transaction Pattern**
```python
try:
    # Create/update models
    model = Model(data)
    db.session.add(model)
    db.session.commit()

    return {'success': True, 'data': model.to_dict()}

except Exception as e:
    db.session.rollback()
    return {'success': False, 'error': str(e)}
```

---

**🔄 Next**: [Frontend Deep Dive](README-Frontend.md) - Learn about HTML, CSS, and JavaScript integration
