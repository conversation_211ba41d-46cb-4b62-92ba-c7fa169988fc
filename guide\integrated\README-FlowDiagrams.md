# 🔄 Flow Diagrams & Services - Dr. Resume Integration

## 📋 **Table of Contents**
1. [System Architecture Flow](#system-architecture-flow)
2. [User Journey Flow](#user-journey-flow)
3. [Data Processing Pipeline](#data-processing-pipeline)
4. [Service Implementation Details](#service-implementation-details)
5. [API Request Flow](#api-request-flow)
6. [Database Interaction Flow](#database-interaction-flow)
7. [Frontend-Backend Communication](#frontend-backend-communication)

---

## 🏗️ **System Architecture Flow**

### **High-Level Architecture**
```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Landing Page] --> B[Registration/Login]
        B --> C[Dashboard]
        C --> D[Upload Resume]
        C --> E[Add Job Description]
        C --> F[Extract Keywords]
        C --> G[Calculate Match]
        C --> H[Generate Suggestions]
        C --> I[Account Management]
    end
    
    subgraph "Backend Layer"
        J[Flask Application]
        K[Authentication Middleware]
        L[Route Handlers]
        M[Service Layer]
        N[Database Models]
    end
    
    subgraph "Data Layer"
        O[SQLite Database]
        P[File Storage]
        Q[NLP Models]
    end
    
    A --> J
    B --> K
    D --> P
    F --> Q
    L --> M
    M --> N
    N --> O
```

### **Service Architecture**
```mermaid
graph LR
    subgraph "Core Services"
        A[FileParser Service]
        B[KeywordParser Service]
        C[MatchingService]
        D[DynamicSuggestionsService]
    end
    
    subgraph "Support Services"
        E[Authentication Service]
        F[Database Service]
        G[Logging Service]
    end
    
    A --> B
    B --> C
    C --> D
    E --> A
    E --> B
    E --> C
    E --> D
    F --> A
    F --> B
    F --> C
    F --> D
```

---

## 👤 **User Journey Flow**

### **Complete User Experience**
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant DB as Database
    participant NLP as NLP Service
    
    Note over U,NLP: User Registration & Login (US1-US2)
    U->>F: Access Landing Page
    F->>U: Show Registration Form
    U->>F: Submit Registration Data
    F->>B: POST /api/register
    B->>DB: Create User Record
    DB-->>B: User Created
    B-->>F: Registration Success
    F-->>U: Redirect to Login
    
    U->>F: Submit Login Credentials
    F->>B: POST /api/login
    B->>DB: Validate User
    DB-->>B: User Valid
    B-->>F: JWT Token
    F-->>U: Redirect to Dashboard
    
    Note over U,NLP: Document Management (US3-US4)
    U->>F: Upload Resume
    F->>B: POST /api/upload_resume (with file)
    B->>B: Parse PDF/DOCX
    B->>DB: Save Resume Record
    DB-->>B: Resume Saved
    B-->>F: Upload Success
    
    U->>F: Add Job Description
    F->>B: POST /api/job_descriptions
    B->>DB: Save Job Description
    DB-->>B: JD Saved
    B-->>F: JD Created
    
    Note over U,NLP: Analysis & Matching (US5-US6)
    U->>F: Extract Keywords
    F->>B: POST /api/extract_keywords
    B->>NLP: Process Text with spaCy
    NLP-->>B: Keywords Extracted
    B->>DB: Update Records with Keywords
    DB-->>B: Keywords Saved
    B-->>F: Extraction Complete
    
    U->>F: Calculate Match Score
    F->>B: POST /api/calculate_match
    B->>B: Apply Jaccard Algorithm
    B->>DB: Save Match Score
    DB-->>B: Score Saved
    B-->>F: Match Score Result
    
    Note over U,NLP: AI Suggestions (US7)
    U->>F: Generate Suggestions
    F->>B: POST /api/basic_suggestions
    B->>B: Analyze Resume vs JD
    B->>DB: Save Suggestions
    DB-->>B: Suggestions Saved
    B-->>F: Suggestions Generated
    F-->>U: Display Recommendations
```

---

## 🔄 **Data Processing Pipeline**

### **Document Processing Flow**
```mermaid
flowchart TD
    A[User Uploads File] --> B{File Type?}
    B -->|PDF| C[PyPDF2 Parser]
    B -->|DOCX| D[python-docx Parser]
    C --> E[Extract Raw Text]
    D --> E
    E --> F[Clean & Normalize Text]
    F --> G[Store in Database]
    G --> H[Trigger Keyword Extraction]
    H --> I[spaCy NLP Processing]
    I --> J{Keyword Categories}
    J --> K[Technical Skills]
    J --> L[Soft Skills]
    J --> M[Other Keywords]
    K --> N[Store Categorized Keywords]
    L --> N
    M --> N
    N --> O[Ready for Matching]
```

### **Matching Algorithm Flow**
```mermaid
flowchart TD
    A[Resume Keywords] --> C[Jaccard Similarity]
    B[Job Description Keywords] --> C
    C --> D{Calculate by Category}
    D --> E[Technical Skills Score]
    D --> F[Soft Skills Score]
    D --> G[Other Keywords Score]
    E --> H[Weighted Average]
    F --> H
    G --> H
    H --> I[Overall Match Score]
    I --> J[Store in Database]
    J --> K[Generate Suggestions]
```

### **Suggestion Generation Flow**
```mermaid
flowchart TD
    A[Match Score Data] --> B[Analyze Gaps]
    B --> C{Missing Keywords?}
    C -->|Yes| D[Generate Keyword Suggestions]
    C -->|No| E[Generate Optimization Suggestions]
    D --> F[Categorize by Priority]
    E --> F
    F --> G{Suggestion Type}
    G -->|Basic| H[6 Core Categories]
    G -->|Premium| I[Advanced Categories]
    H --> J[Format Suggestions]
    I --> J
    J --> K[Store in Database]
    K --> L[Return to User]
```

---

## ⚙️ **Service Implementation Details**

### **1. FileParser Service (US3)**
```python
class FileParser:
    """
    Purpose: Extract text from uploaded documents
    Dependencies: PyPDF2, python-docx
    Input: File path, file type
    Output: Extracted text, metadata
    """
    
    def __init__(self):
        self.supported_types = ['pdf', 'docx', 'doc']
        self.max_file_size = 16 * 1024 * 1024  # 16MB
    
    def parse_file(self, file_path: str, file_type: str) -> Dict:
        """Main parsing method"""
        # Validation → Type Detection → Parsing → Text Cleaning
        
    def _parse_pdf(self, file_path: str) -> Dict:
        """PDF-specific parsing using PyPDF2"""
        # Page extraction → Text combination → Formatting cleanup
        
    def _parse_docx(self, file_path: str) -> Dict:
        """DOCX-specific parsing using python-docx"""
        # Paragraph extraction → Table handling → Style preservation
```

### **2. KeywordParser Service (US5)**
```python
class KeywordParser:
    """
    Purpose: Extract and categorize keywords using NLP
    Dependencies: spaCy, NLTK
    Input: Raw text
    Output: Categorized keywords
    """
    
    def __init__(self):
        self.nlp = spacy.load('en_core_web_sm')
        self.technical_patterns = self._load_technical_patterns()
        self.soft_skills_patterns = self._load_soft_skills_patterns()
    
    def extract_keywords(self, text: str) -> Dict:
        """Main extraction method"""
        # Text preprocessing → NLP processing → Categorization → Filtering
        
    def _extract_technical_skills(self, doc) -> List[str]:
        """Extract technical skills using pattern matching"""
        # Entity recognition → Pattern matching → Skill validation
        
    def _extract_soft_skills(self, doc) -> List[str]:
        """Extract soft skills using predefined patterns"""
        # Phrase detection → Context analysis → Skill extraction
```

### **3. MatchingService (US6)**
```python
class MatchingService:
    """
    Purpose: Calculate compatibility between resume and job description
    Dependencies: Database models
    Input: Resume ID, Job Description ID
    Output: Match scores, detailed analysis
    """
    
    def __init__(self):
        self.algorithm = 'jaccard'
        self.weights = {
            'technical_skills': 0.6,
            'soft_skills': 0.3,
            'other_keywords': 0.1
        }
    
    def calculate_match_score(self, resume_id: int, jd_id: int, user_id: int) -> Dict:
        """Main matching method"""
        # Data retrieval → Keyword comparison → Score calculation → Storage
        
    def _calculate_jaccard_similarity(self, set1: List[str], set2: List[str]) -> float:
        """Jaccard similarity: |A ∩ B| / |A ∪ B|"""
        # Set operations → Similarity calculation → Normalization
```

### **4. DynamicSuggestionsService (US7)**
```python
class DynamicSuggestionsService:
    """
    Purpose: Generate AI-powered resume optimization suggestions
    Dependencies: MatchingService, KeywordParser
    Input: Resume ID, Job Description ID, User ID
    Output: Categorized suggestions with priorities
    """
    
    def __init__(self):
        self.matching_service = MatchingService()
        self.keyword_parser = KeywordParser()
        self.suggestion_categories = self._load_categories()
    
    def generate_basic_suggestions(self, resume_id: int, jd_id: int, user_id: int) -> Dict:
        """Generate basic suggestions (6 categories)"""
        # Analysis → Gap identification → Suggestion generation → Prioritization
        
    def generate_premium_suggestions(self, resume_id: int, jd_id: int, user_id: int) -> Dict:
        """Generate premium suggestions (advanced categories)"""
        # Enhanced analysis → Advanced recommendations → Detailed examples
```

---

## 🌐 **API Request Flow**

### **Authentication Flow**
```mermaid
sequenceDiagram
    participant C as Client
    participant M as Middleware
    participant R as Route Handler
    participant S as Service
    participant DB as Database
    
    C->>M: Request with JWT Token
    M->>M: Validate JWT Token
    alt Token Valid
        M->>R: Forward Request
        R->>S: Call Service Method
        S->>DB: Database Operation
        DB-->>S: Data Response
        S-->>R: Service Response
        R-->>M: Route Response
        M-->>C: Success Response
    else Token Invalid
        M-->>C: 401 Unauthorized
    end
```

### **File Upload Flow**
```mermaid
sequenceDiagram
    participant C as Client
    participant R as Route Handler
    participant F as FileParser
    participant FS as File System
    participant DB as Database
    
    C->>R: POST /api/upload_resume (multipart/form-data)
    R->>R: Validate File Type & Size
    R->>FS: Save File to Upload Directory
    FS-->>R: File Path
    R->>F: Parse File Content
    F->>FS: Read File
    FS-->>F: File Content
    F->>F: Extract Text
    F-->>R: Extracted Text
    R->>DB: Create Resume Record
    DB-->>R: Resume Created
    R-->>C: Upload Success Response
```

---

## 🗄️ **Database Interaction Flow**

### **Model Relationships**
```mermaid
erDiagram
    User ||--o{ Resume : owns
    User ||--o{ JobDescription : creates
    User ||--o{ MatchScore : has
    User ||--o{ Suggestion : receives
    
    Resume ||--o{ MatchScore : participates_in
    JobDescription ||--o{ MatchScore : participates_in
    
    Resume {
        int id PK
        int user_id FK
        string original_filename
        text extracted_text
        json technical_skills
        json soft_skills
        boolean keywords_extracted
    }
    
    JobDescription {
        int id PK
        int user_id FK
        string title
        string company_name
        text job_text
        json technical_skills
        json soft_skills
        boolean keywords_extracted
    }
    
    MatchScore {
        int id PK
        int user_id FK
        int resume_id FK
        int job_description_id FK
        float overall_score
        float technical_score
        float soft_skills_score
    }
    
    Suggestion {
        int id PK
        int user_id FK
        int resume_id FK
        int job_description_id FK
        string category
        string title
        text description
        string priority
    }
```

### **Database Transaction Flow**
```mermaid
flowchart TD
    A[API Request] --> B[Route Handler]
    B --> C[Service Method]
    C --> D[Begin Transaction]
    D --> E[Model Operations]
    E --> F{All Operations Successful?}
    F -->|Yes| G[Commit Transaction]
    F -->|No| H[Rollback Transaction]
    G --> I[Return Success]
    H --> J[Return Error]
    I --> K[Send Response]
    J --> K
```

---

## 🔄 **Frontend-Backend Communication**

### **State Management Flow**
```mermaid
stateDiagram-v2
    [*] --> Unauthenticated
    Unauthenticated --> Authenticating : Login Request
    Authenticating --> Authenticated : JWT Received
    Authenticating --> Unauthenticated : Login Failed
    Authenticated --> Loading : API Request
    Loading --> Authenticated : Request Success
    Loading --> Error : Request Failed
    Error --> Authenticated : Retry/Dismiss
    Authenticated --> Unauthenticated : Logout/Token Expired
```

### **Component Communication**
```mermaid
flowchart LR
    subgraph "Frontend Components"
        A[Page Manager]
        B[Form Handler]
        C[API Client]
        D[UI Updater]
    end
    
    subgraph "Backend Services"
        E[Route Handler]
        F[Business Logic]
        G[Database Layer]
    end
    
    A --> B
    B --> C
    C --> E
    E --> F
    F --> G
    G --> F
    F --> E
    E --> C
    C --> D
    D --> A
```

---

## 📊 **Performance & Monitoring Flow**

### **Request Monitoring**
```mermaid
flowchart TD
    A[Incoming Request] --> B[Middleware Logger]
    B --> C[Route Handler]
    C --> D[Service Execution]
    D --> E[Database Query]
    E --> F[Response Generation]
    F --> G[Response Logger]
    G --> H[Send Response]
    
    B --> I[Log Request Start]
    G --> J[Log Request End]
    I --> K[Performance Metrics]
    J --> K
    K --> L[Monitoring Dashboard]
```

### **Error Handling Flow**
```mermaid
flowchart TD
    A[Exception Occurs] --> B{Exception Type}
    B -->|Validation Error| C[400 Bad Request]
    B -->|Authentication Error| D[401 Unauthorized]
    B -->|Permission Error| E[403 Forbidden]
    B -->|Not Found Error| F[404 Not Found]
    B -->|Server Error| G[500 Internal Server Error]
    
    C --> H[Log Warning]
    D --> H
    E --> H
    F --> H
    G --> I[Log Error]
    
    H --> J[Return Error Response]
    I --> J
```

---

**🔄 This completes the comprehensive flow diagrams and service implementation details for the Dr. Resume integrated application.**

**📚 Complete Documentation Set:**
- [Main Integration Guide](README.md)
- [Backend Deep Dive](README-Backend.md)
- [Frontend Deep Dive](README-Frontend.md)
- [Feature Integration](README-Integration.md)
- [Deployment Guide](README-Deployment.md)
- **[Flow Diagrams & Services](README-FlowDiagrams.md)** ← You are here
