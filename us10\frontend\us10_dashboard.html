<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Dr. Resume</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div>
                <h1>🩺 Dr. Resume</h1>
                <nav class="dashboard-nav">
                    <a href="#" class="nav-item active">📊 Dashboard</a>
                    <a href="/add-job-description" class="nav-item">📄 Add Job Description</a>
                    <a href="/upload" class="nav-item">📤 Upload Resume</a>
                    <a href="/keywords" class="nav-item">🔍 Keywords</a>
                    <a href="/matching" class="nav-item">🎯 Matching</a>
                    <a href="/suggestions" class="nav-item">💡 Suggestions</a>
                    <a href="/account" class="nav-item">⚙️ Account</a>
                </nav>
            </div>
            
            <div class="user-info">
                <span id="welcomeMessage">Welcome, GUEST</span>
                <button class="logout-btn" onclick="logout()">🔐 Login</button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <h2 style="margin-bottom: 30px; color: #1f2937;">Welcome, <span id="userName">GUEST</span></h2>
            <p style="color: #6b7280; margin-bottom: 40px;">AI-Powered Resume Scanner & Job Matching Platform</p>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-icon">📄</div>
                    <h3 class="card-title">Job Descriptions</h3>
                    <p class="card-description" id="jdCardDescription">No job descriptions yet. Add one to get started!</p>
                    <button class="card-button" onclick="addJobDescription()">
                        ➕ Add Job Description
                    </button>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">🔍</div>
                    <h3 class="card-title">Keywords Analysis</h3>
                    <p class="card-description">Extract and analyze keywords from your resumes and job descriptions</p>
                    <button class="card-button" onclick="analyzeKeywords()">
                        🔍 Analyze Keywords
                    </button>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-icon">📋</div>
                    <h3 class="card-title">Resumes</h3>
                    <p class="card-description" id="resumeCardDescription">No resumes uploaded yet. Upload one to get started!</p>
                    <button class="card-button" onclick="uploadResume()">
                        📤 Upload Resume
                    </button>
                </div>
            </div>
            
            <!-- Stats Section -->
            <div style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-top: 30px;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📊 Dashboard</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;" id="resumeCount">0</div>
                        <div style="color: #6b7280;">Resumes</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;" id="jdCount">0</div>
                        <div style="color: #6b7280;">Job Descriptions</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;">0</div>
                        <div style="color: #6b7280;">Scans</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;">0%</div>
                        <div style="color: #6b7280;">Avg. Match</div>
                    </div>
                </div>
            </div>
            
            <!-- Job Description List Section -->
            <div class="jd-list" id="jdListSection" style="display: none;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📄 Your Job Descriptions</h3>
                <div id="jdList"></div>
            </div>

            <!-- Resume List Section -->
            <div class="resume-list" id="resumeListSection" style="display: none;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📋 Your Resumes</h3>
                <div id="resumeList"></div>
            </div>
            
            <!-- Scan History Section -->
            <div style="margin-top: 30px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="color: #1f2937; margin: 0;">📈 Recent Scan History</h3>
                    <div style="display: flex; gap: 10px;">
                        <select id="scoreFilter" onchange="filterHistory()" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 6px;">
                            <option value="">All Scores</option>
                            <option value="excellent">Excellent (80-100%)</option>
                            <option value="good">Good (60-79%)</option>
                            <option value="fair">Fair (40-59%)</option>
                            <option value="poor">Poor (0-39%)</option>
                        </select>
                        <button onclick="refreshHistory()" style="padding: 8px 12px; background: #7c3aed; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            🔄 Refresh
                        </button>
                    </div>
                </div>

                <div id="scanHistoryContainer">
                    <div id="scanHistoryLoading" style="text-align: center; padding: 40px; color: #6b7280;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
                        Loading scan history...
                    </div>
                    <div id="scanHistoryEmpty" style="display: none; text-align: center; padding: 40px; color: #6b7280;">
                        <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                        <h4 style="margin: 0 0 10px 0;">No scan history yet</h4>
                        <p style="margin: 0;">Upload a resume and job description to start scanning!</p>
                    </div>
                    <div id="scanHistoryList"></div>
                </div>

                <div id="historyPagination" style="display: none; text-align: center; margin-top: 20px;">
                    <button id="prevPageBtn" onclick="changePage(-1)" style="padding: 8px 16px; margin: 0 5px; background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                        ← Previous
                    </button>
                    <span id="pageInfo" style="margin: 0 15px; color: #6b7280;"></span>
                    <button id="nextPageBtn" onclick="changePage(1)" style="padding: 8px 16px; margin: 0 5px; background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                        Next →
                    </button>
                </div>
            </div>

            <!-- Quick Actions Section -->
            <div style="margin-top: 30px;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">⚡ Quick Actions</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <button class="card-button" onclick="uploadResume()" style="padding: 16px;">
                        📤 Upload Resume
                    </button>
                    <button class="card-button" onclick="addJobDescription()" style="padding: 16px;">
                        ➕ Add Job Description
                    </button>
                    <button class="card-button" onclick="analyzeKeywords()" style="padding: 16px;">
                        🔍 Analyze Keywords
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/us10_dashboard.js"></script>
</body>
</html>
