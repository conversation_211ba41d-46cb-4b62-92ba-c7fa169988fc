# 🚀 Deployment Guide - Dr. Resume Integration

## 📋 **Table of Contents**
1. [Quick Start Guide](#quick-start-guide)
2. [Local Development Setup](#local-development-setup)
3. [Production Deployment](#production-deployment)
4. [Free Hosting Options](#free-hosting-options)
5. [Environment Configuration](#environment-configuration)
6. [Database Setup](#database-setup)
7. [Troubleshooting](#troubleshooting)

---

## ⚡ **Quick Start Guide**

### **Prerequisites**
- **Python 3.8+** (Check: `python --version`)
- **pip** (Python package manager)
- **2GB free disk space**
- **Internet connection** (for dependencies)

### **5-Minute Setup**
```bash
# 1. Navigate to the integrated application
cd us10/backend

# 2. Install Python dependencies
pip install -r requirements.txt

# 3. Download NLP model (required for keyword extraction)
python -m spacy download en_core_web_sm

# 4. Start the application
python start_app.py

# 5. Open your browser
# Go to: http://localhost:5000
```

### **First Time Setup**
1. **Register Account**: Create your first user account
2. **Upload Resume**: Test with a PDF or DOCX file
3. **Add Job Description**: Create a sample job posting
4. **Extract Keywords**: Process both documents
5. **Generate Suggestions**: Get AI-powered recommendations

---

## 🏠 **Local Development Setup**

### **Detailed Installation**

#### **Step 1: Environment Setup**
```bash
# Create virtual environment (recommended)
python -m venv dr_resume_env

# Activate virtual environment
# Windows:
dr_resume_env\Scripts\activate
# macOS/Linux:
source dr_resume_env/bin/activate

# Verify Python version
python --version  # Should be 3.8+
```

#### **Step 2: Install Dependencies**
```bash
# Navigate to backend directory
cd us10/backend

# Install all required packages
pip install -r requirements.txt

# Install NLP model for keyword extraction
python -m spacy download en_core_web_sm

# Verify installation
python -c "import spacy; print('spaCy installed successfully')"
```

#### **Step 3: Database Setup**
```bash
# The database will be automatically created at:
# shared/database/dr_resume_dev.db

# Verify database location
python -c "
import os
db_path = '../../shared/database/dr_resume_dev.db'
print(f'Database will be created at: {os.path.abspath(db_path)}')
"
```

#### **Step 4: Start Development Server**
```bash
# Start the Flask application
python start_app.py

# You should see:
# 🚀 Starting Dr. Resume Application
# 📍 Available at: http://localhost:5000
# 🔧 Debug mode: OFF (for stability)
```

#### **Step 5: Verify Installation**
```bash
# Test the health endpoint
curl http://localhost:5000/health

# Expected response:
# {"message":"Dr. Resume is running","status":"healthy","version":"1.0.0"}
```

### **Development Workflow**
```bash
# Start development server
python start_app.py

# In another terminal, test API endpoints
curl -X POST http://localhost:5000/api/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123","first_name":"Test","last_name":"User"}'

# Access the application
open http://localhost:5000  # macOS
start http://localhost:5000  # Windows
```

---

## 🌐 **Production Deployment**

### **Production Checklist**
- [ ] Change secret keys in production
- [ ] Use PostgreSQL instead of SQLite
- [ ] Set up SSL/HTTPS
- [ ] Configure environment variables
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy

### **Environment Variables**
Create a `.env` file for production:
```bash
# .env file
FLASK_ENV=production
SECRET_KEY=your-super-secret-production-key
JWT_SECRET_KEY=your-jwt-secret-production-key
DATABASE_URL=postgresql://user:password@host:port/database
UPLOAD_FOLDER=/app/uploads
MAX_CONTENT_LENGTH=16777216
CORS_ORIGINS=https://yourdomain.com
```

### **Production Configuration**
```python
# config.py modifications for production
import os

class ProductionConfig:
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', '/app/uploads')
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16777216))
    
    # Security settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
```

---

## 🆓 **Free Hosting Options**

### **1. Heroku (Recommended for Beginners)**

#### **Setup Steps**
```bash
# 1. Install Heroku CLI
# Download from: https://devcenter.heroku.com/articles/heroku-cli

# 2. Login to Heroku
heroku login

# 3. Create Heroku app
heroku create your-app-name

# 4. Add PostgreSQL database
heroku addons:create heroku-postgresql:hobby-dev

# 5. Set environment variables
heroku config:set SECRET_KEY=your-secret-key
heroku config:set JWT_SECRET_KEY=your-jwt-secret-key

# 6. Deploy
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

#### **Heroku Configuration Files**

**Procfile**:
```
web: gunicorn app_fixed:app
```

**runtime.txt**:
```
python-3.9.16
```

**requirements.txt** (add production dependencies):
```
# ... existing dependencies ...
gunicorn==20.1.0
psycopg2-binary==2.9.5
```

### **2. Railway**

#### **Setup Steps**
```bash
# 1. Install Railway CLI
npm install -g @railway/cli

# 2. Login to Railway
railway login

# 3. Initialize project
railway init

# 4. Add PostgreSQL database
railway add postgresql

# 5. Deploy
railway up
```

#### **Railway Configuration**
Create `railway.json`:
```json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "python start_app.py",
    "healthcheckPath": "/health"
  }
}
```

### **3. Render**

#### **Setup Steps**
1. **Connect GitHub**: Link your repository to Render
2. **Create Web Service**: Choose Python environment
3. **Configure Build**: Set build and start commands
4. **Add Database**: Create PostgreSQL instance
5. **Set Environment Variables**: Configure production settings

#### **Render Configuration**
**Build Command**:
```bash
pip install -r requirements.txt && python -m spacy download en_core_web_sm
```

**Start Command**:
```bash
python start_app.py
```

### **4. PythonAnywhere**

#### **Setup Steps**
1. **Upload Code**: Use file manager or git
2. **Install Dependencies**: Use Bash console
3. **Configure WSGI**: Set up web app
4. **Database Setup**: Use MySQL or PostgreSQL

#### **WSGI Configuration**
```python
# /var/www/yourusername_pythonanywhere_com_wsgi.py
import sys
import os

# Add your project directory to sys.path
sys.path.insert(0, '/home/<USER>/dr-resume')

from app_fixed import create_app
application = create_app()
```

---

## ⚙️ **Environment Configuration**

### **Development Environment**
```python
# config.py
class DevelopmentConfig:
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///../../shared/database/dr_resume_dev.db'
    SECRET_KEY = 'dev-secret-key'
    JWT_SECRET_KEY = 'dev-jwt-secret-key'
```

### **Production Environment**
```python
# config.py
class ProductionConfig:
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    
    # Security headers
    SECURITY_HEADERS = {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block'
    }
```

### **Environment Detection**
```python
# app_fixed.py
import os

def create_app():
    app = Flask(__name__)
    
    # Detect environment
    env = os.environ.get('FLASK_ENV', 'development')
    
    if env == 'production':
        app.config.from_object(ProductionConfig)
    else:
        app.config.from_object(DevelopmentConfig)
    
    return app
```

---

## 🗄️ **Database Setup**

### **SQLite (Development)**
```python
# Automatic setup - no configuration needed
# Database created at: shared/database/dr_resume_dev.db
```

### **PostgreSQL (Production)**
```python
# Install PostgreSQL adapter
pip install psycopg2-binary

# Connection string format
DATABASE_URL = "postgresql://username:password@host:port/database"

# Example for Heroku
DATABASE_URL = "postgresql://user:<EMAIL>:5432/dbname"
```

### **Database Migration**
```python
# For production deployment with existing data
def migrate_to_postgresql():
    # Export SQLite data
    sqlite_data = export_sqlite_data()
    
    # Import to PostgreSQL
    import_to_postgresql(sqlite_data)
```

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **1. spaCy Model Not Found**
```bash
# Error: Can't find model 'en_core_web_sm'
# Solution:
python -m spacy download en_core_web_sm

# Verify installation:
python -c "import spacy; spacy.load('en_core_web_sm'); print('Model loaded successfully')"
```

#### **2. Database Connection Error**
```bash
# Error: sqlite3.OperationalError: unable to open database file
# Solution: Check database path and permissions
python -c "
import os
db_path = '../../shared/database/dr_resume_dev.db'
print(f'Database path: {os.path.abspath(db_path)}')
print(f'Directory exists: {os.path.exists(os.path.dirname(os.path.abspath(db_path)))}')
"
```

#### **3. File Upload Issues**
```bash
# Error: File upload fails
# Solution: Check upload directory permissions
mkdir -p ../../shared/uploads
chmod 755 ../../shared/uploads
```

#### **4. JWT Token Errors**
```bash
# Error: JWT decode error
# Solution: Check secret key configuration
python -c "
import os
print(f'JWT_SECRET_KEY set: {bool(os.environ.get(\"JWT_SECRET_KEY\"))}')
"
```

### **Debug Mode**
```python
# Enable debug mode for troubleshooting
# In app_fixed.py, change:
app.run(host='0.0.0.0', port=5000, debug=True)
```

### **Logging Configuration**
```python
# Add detailed logging
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

---

## 📊 **Performance Optimization**

### **Production Optimizations**
```python
# Use production WSGI server
pip install gunicorn

# Start with Gunicorn
gunicorn --bind 0.0.0.0:5000 --workers 4 app_fixed:app

# Enable gzip compression
from flask_compress import Compress
Compress(app)

# Add caching
from flask_caching import Cache
cache = Cache(app, config={'CACHE_TYPE': 'simple'})
```

### **Database Optimizations**
```python
# Add database indexes
class Resume(db.Model):
    # ... existing fields ...
    __table_args__ = (
        db.Index('idx_user_id', 'user_id'),
        db.Index('idx_keywords_extracted', 'keywords_extracted'),
    )
```

---

## 🎯 **Deployment Success Checklist**

- [ ] **Application starts without errors**
- [ ] **Health endpoint responds correctly**
- [ ] **User registration works**
- [ ] **File upload functions properly**
- [ ] **Keyword extraction processes correctly**
- [ ] **Suggestions generate successfully**
- [ ] **Database persists data**
- [ ] **SSL/HTTPS configured (production)**
- [ ] **Environment variables set**
- [ ] **Monitoring configured**

---

---

## 🎯 **Deployment Success Checklist**

- [ ] **Application starts without errors**
- [ ] **Health endpoint responds correctly**
- [ ] **User registration works**
- [ ] **File upload functions properly**
- [ ] **Keyword extraction processes correctly**
- [ ] **Suggestions generate successfully**
- [ ] **Database persists data**
- [ ] **SSL/HTTPS configured (production)**
- [ ] **Environment variables set**
- [ ] **Monitoring configured**

---

**🎉 Congratulations! Your Dr. Resume application is now deployed and ready for users!**

**📚 Complete Documentation Set:**
- [Main Integration Guide](README.md)
- [Backend Deep Dive](README-Backend.md)
- [Frontend Deep Dive](README-Frontend.md)
- [Feature Integration](README-Integration.md)
- **[Deployment Guide](README-Deployment.md)** ← You are here
- [Flow Diagrams & Services](README-FlowDiagrams.md)
