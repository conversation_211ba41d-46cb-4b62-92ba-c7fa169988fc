<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matching Score - Dr. Resume</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1>🎯 Resume-Job Matching</h1>
                <p>Compare your resume with job descriptions and get detailed matching scores</p>
            </div>
            <nav class="nav">
                <a href="/dashboard" class="nav-link">📊 Dashboard</a>
                <a href="/upload" class="nav-link">📤 Upload</a>
                <a href="/add-job-description" class="nav-link">📄 Add JD</a>
                <a href="/keywords" class="nav-link">🔍 Keywords</a>
                <a href="/matching" class="nav-link active">🎯 Matching</a>
                <button id="logoutBtn" class="nav-link logout-btn">🚪 Logout</button>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Calculate Match Section -->
            <section class="match-calculator">
                <h2>Calculate New Match</h2>
                <div class="calculator-form">
                    <div class="form-group">
                        <label for="resumeSelect">Select Resume:</label>
                        <select id="resumeSelect" class="form-select">
                            <option value="">Loading resumes...</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="jobSelect">Select Job Description:</label>
                        <select id="jobSelect" class="form-select">
                            <option value="">Loading job descriptions...</option>
                        </select>
                    </div>
                    
                    <button id="calculateBtn" class="btn btn-primary" disabled>
                        🎯 Calculate Match Score
                    </button>
                </div>
            </section>

            <!-- Loading Section -->
            <section id="loadingSection" class="loading-section" style="display: none;">
                <div class="loading-spinner"></div>
                <p>Calculating match score...</p>
            </section>

            <!-- Match Result Section -->
            <section id="matchResult" class="match-result" style="display: none;">
                <h2>Match Score Result</h2>
                <div class="score-display">
                    <div class="overall-score">
                        <div class="score-circle">
                            <div class="score-value" id="overallScore">0%</div>
                            <div class="score-label">Overall Match</div>
                        </div>
                    </div>
                    
                    <div class="detailed-scores">
                        <div class="score-item">
                            <div class="score-category">Technical Skills</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="technicalProgress"></div>
                            </div>
                            <div class="score-percentage" id="technicalScore">0%</div>
                        </div>
                        
                        <div class="score-item">
                            <div class="score-category">Soft Skills</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="softSkillsProgress"></div>
                            </div>
                            <div class="score-percentage" id="softSkillsScore">0%</div>
                        </div>
                        
                        <div class="score-item">
                            <div class="score-category">Other Keywords</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="otherProgress"></div>
                            </div>
                            <div class="score-percentage" id="otherScore">0%</div>
                        </div>
                    </div>
                </div>
                
                <div class="match-details">
                    <div class="detail-item">
                        <span class="detail-label">Matched Keywords:</span>
                        <span class="detail-value" id="matchedKeywords">0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Total Resume Keywords:</span>
                        <span class="detail-value" id="totalResumeKeywords">0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Total JD Keywords:</span>
                        <span class="detail-value" id="totalJdKeywords">0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Algorithm Used:</span>
                        <span class="detail-value" id="algorithmUsed">Jaccard Similarity</span>
                    </div>
                </div>
            </section>

            <!-- Match History Section -->
            <section class="match-history">
                <h2>Recent Match History</h2>
                <div id="historyContainer" class="history-container">
                    <div class="loading-text">Loading match history...</div>
                </div>
            </section>
        </main>

        <!-- Error Messages -->
        <div id="errorMessage" class="error-message" style="display: none;"></div>
        <div id="successMessage" class="success-message" style="display: none;"></div>
    </div>

    <script src="/static/js/us10_matching.js"></script>
</body>
</html>
