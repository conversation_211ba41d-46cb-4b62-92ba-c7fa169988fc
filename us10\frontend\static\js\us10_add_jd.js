// Dr. Resume US-04 Add Job Description JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Add Job Description Page Loaded (US-04)');
    
    // Check authentication
    checkAuthentication();
    
    // Initialize form functionality
    initializeForm();
});

function checkAuthentication() {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        console.log('❌ No token found, redirecting to login');
        window.location.href = '/login';
        return;
    }
    
    // Verify token (simplified check)
    console.log('✅ Token found, user authenticated');
}

function initializeForm() {
    const form = document.getElementById('jdForm');
    const submitBtn = document.getElementById('submitBtn');
    const jobText = document.getElementById('jobText');
    const characterCounter = document.getElementById('characterCounter');
    const alertContainer = document.getElementById('alertContainer');
    
    // Character counter functionality
    jobText.addEventListener('input', function() {
        updateCharacterCounter();
    });
    
    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Clear previous alerts
        clearAlerts();
        
        // Get form data
        const formData = new FormData(form);
        const data = {
            title: formData.get('title').trim(),
            company_name: formData.get('company_name').trim(),
            job_text: formData.get('job_text').trim()
        };
        
        // Client-side validation
        const validationErrors = validateForm(data);
        if (validationErrors.length > 0) {
            showAlert('error', 'Please fix the following errors:', validationErrors);
            return;
        }
        
        // Show loading state
        setLoading(true);
        
        try {
            await submitJobDescription(data);
        } catch (error) {
            console.error('Submission error:', error);
            showAlert('error', 'Network error. Please check your connection and try again.');
        } finally {
            setLoading(false);
        }
    });
    
    // Initialize character counter
    updateCharacterCounter();
    
    function updateCharacterCounter() {
        const text = jobText.value;
        const length = text.length;
        const maxLength = 50000;
        
        characterCounter.textContent = `${length.toLocaleString()} / ${maxLength.toLocaleString()} characters`;
        
        // Update counter styling based on length
        characterCounter.classList.remove('warning', 'error');
        
        if (length > maxLength * 0.9) {
            characterCounter.classList.add('warning');
        }
        
        if (length > maxLength) {
            characterCounter.classList.add('error');
        }
        
        // Update word count display
        const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
        const wordCountText = ` (${wordCount.toLocaleString()} words)`;
        
        if (!characterCounter.textContent.includes('words')) {
            characterCounter.textContent += wordCountText;
        }
    }
    
    async function submitJobDescription(data) {
        const token = localStorage.getItem('dr_resume_token');
        
        if (!token) {
            showAlert('error', 'Authentication required. Please log in again.');
            window.location.href = '/login';
            return;
        }
        
        const response = await fetch('/api/upload_jd', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('success', result.message);
            
            // Reset form
            form.reset();
            updateCharacterCounter();
            
            // Redirect to dashboard after 2 seconds
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);
            
        } else {
            if (result.errors && result.errors.length > 0) {
                showAlert('error', result.message, result.errors);
            } else {
                showAlert('error', result.message);
            }
        }
    }
    
    function validateForm(data) {
        const errors = [];
        
        // Title validation
        if (!data.title) {
            errors.push('Job title is required');
        } else if (data.title.length < 3) {
            errors.push('Job title must be at least 3 characters long');
        } else if (data.title.length > 255) {
            errors.push('Job title must be less than 255 characters');
        }
        
        // Job text validation
        if (!data.job_text) {
            errors.push('Job description text is required');
        } else if (data.job_text.length < 50) {
            errors.push('Job description must be at least 50 characters long');
        } else if (data.job_text.length > 50000) {
            errors.push('Job description must be less than 50,000 characters');
        }
        
        // Company name validation (optional)
        if (data.company_name && data.company_name.length > 255) {
            errors.push('Company name must be less than 255 characters');
        }
        
        return errors;
    }
    
    function setLoading(loading) {
        if (loading) {
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
            submitBtn.textContent = 'Saving...';
        } else {
            submitBtn.disabled = false;
            submitBtn.classList.remove('loading');
            submitBtn.textContent = '💾 Save Job Description';
        }
    }
}

// UI Helper functions
function showAlert(type, message, errors = []) {
    const alertContainer = document.getElementById('alertContainer');
    
    let alertHTML = `<div class="alert alert-${type}">`;
    alertHTML += `<strong>${message}</strong>`;
    
    if (errors.length > 0) {
        alertHTML += '<ul style="margin: 8px 0 0 20px;">';
        errors.forEach(error => {
            alertHTML += `<li>${error}</li>`;
        });
        alertHTML += '</ul>';
    }
    
    alertHTML += '</div>';
    
    alertContainer.innerHTML = alertHTML;
    
    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}
