#!/usr/bin/env python3
"""
Dr. Resume Application Startup Script with Health Check
Installs dependencies, sets up environment, starts app, and runs health check
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser
from pathlib import Path

def run_command(command, description, check_output=False):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        if check_output:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        else:
            subprocess.run(command, shell=True, check=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if check_output and e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found")
        return False
    
    # Install packages
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python packages"
    )

def download_nlp_models():
    """Download required NLP models"""
    print("🧠 Setting up NLP models...")
    
    # Download NLTK data
    nltk_command = f"""{sys.executable} -c "
import nltk
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('averaged_perceptron_tagger', quiet=True)
    print('NLTK models downloaded successfully')
except Exception as e:
    print(f'NLTK download error: {{e}}')
"
"""
    
    if not run_command(nltk_command, "Downloading NLTK models"):
        print("⚠️ NLTK models download failed, but continuing...")
    
    # Download SpaCy model
    spacy_command = f"{sys.executable} -m spacy download en_core_web_sm"
    if not run_command(spacy_command, "Downloading SpaCy model"):
        print("⚠️ SpaCy model download failed, but continuing...")

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    # Get the project root (two levels up from current directory)
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent
    
    directories = [
        project_root / "shared" / "database",
        project_root / "shared" / "uploads",
        current_dir / "logs"
    ]
    
    for directory in directories:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True

def initialize_database():
    """Initialize the database"""
    print("🗄️ Initializing database...")
    
    init_command = f"""{sys.executable} -c "
import sys
import os
sys.path.append('.')

try:
    from app_fixed import create_app
    from models import db
    
    app = create_app()
    with app.app_context():
        db.create_all()
        print('Database initialized successfully')
except Exception as e:
    print(f'Database initialization error: {{e}}')
    import traceback
    traceback.print_exc()
"
"""
    
    return run_command(init_command, "Initializing database")

def start_application():
    """Start the Flask application in a separate thread"""
    print("🚀 Starting Dr. Resume application...")
    
    def run_app():
        try:
            # Import and run the app
            from app_fixed import main
            main()
        except Exception as e:
            print(f"❌ Application startup error: {e}")
    
    # Start app in a separate thread
    app_thread = threading.Thread(target=run_app, daemon=True)
    app_thread.start()
    
    # Wait for the app to start
    print("⏳ Waiting for application to start...")
    time.sleep(5)
    
    return app_thread

def wait_for_server(url="http://localhost:5000", timeout=30):
    """Wait for the server to be ready"""
    import requests
    
    print(f"⏳ Waiting for server at {url}...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n❌ Server did not start within {timeout} seconds")
    return False

def run_health_check():
    """Run the comprehensive health check"""
    print("🩺 Running comprehensive health check...")
    
    try:
        # Import and run the health check
        from deep_health_check import DrResumeHealthCheck
        
        health_check = DrResumeHealthCheck()
        success = health_check.run_all_tests()
        
        if success:
            print("🎉 All health checks passed!")
            return True
        else:
            print("⚠️ Some health checks failed. See details above.")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def open_browser():
    """Open the application in the default browser"""
    print("🌐 Opening application in browser...")
    try:
        webbrowser.open("http://localhost:5000")
        print("✅ Browser opened successfully")
    except Exception as e:
        print(f"⚠️ Could not open browser: {e}")
        print("Please manually open: http://localhost:5000")

def main():
    """Main startup function"""
    print("🩺" + "="*50 + "🩺")
    print("🚀 Dr. Resume Application Startup & Health Check")
    print("="*52)
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Step 1: Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Set up directories
    if not setup_directories():
        print("❌ Directory setup failed")
        sys.exit(1)
    
    # Step 3: Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed")
        sys.exit(1)
    
    # Step 4: Download NLP models
    download_nlp_models()
    
    # Step 5: Initialize database
    if not initialize_database():
        print("❌ Database initialization failed")
        sys.exit(1)
    
    # Step 6: Start the application
    app_thread = start_application()
    
    # Step 7: Wait for server to be ready
    if not wait_for_server():
        print("❌ Server startup failed")
        sys.exit(1)
    
    # Step 8: Run health check
    health_success = run_health_check()
    
    # Step 9: Open browser
    open_browser()
    
    # Final status
    print("\n" + "="*52)
    if health_success:
        print("🎉 Dr. Resume is running successfully!")
        print("📍 Application URL: http://localhost:5000")
        print("🩺 All health checks passed")
    else:
        print("⚠️ Dr. Resume is running but some issues were detected")
        print("📍 Application URL: http://localhost:5000")
        print("🔍 Check the health check results above")
    
    print("\n💡 Tips:")
    print("   - The application is running in debug mode")
    print("   - Press Ctrl+C to stop the server")
    print("   - Check the console for any error messages")
    print("="*52)
    
    # Keep the main thread alive
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down Dr. Resume...")
        sys.exit(0)

if __name__ == "__main__":
    main()
