#!/usr/bin/env python3
"""
Simple Test Routes for Dr. <PERSON>
Provides test endpoints without authentication requirements
"""

from flask import Blueprint, jsonify
from models import db, User, Resume, JobDescription
from datetime import datetime

# Create blueprint for test routes
test_auth_bp = Blueprint('test_auth', __name__, url_prefix='/api')

@test_auth_bp.route('/test-db-connection', methods=['GET'])
def test_db_connection():
    """Test database connection"""
    try:
        # Try to query the database
        user_count = User.query.count()
        resume_count = Resume.query.count()
        jd_count = JobDescription.query.count()
        
        return jsonify({
            'success': True,
            'message': 'Database connection successful',
            'stats': {
                'users': user_count,
                'resumes': resume_count,
                'job_descriptions': jd_count
            },
            'timestamp': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Database connection failed: {str(e)}'
        }), 500

@test_auth_bp.route('/test-create-user', methods=['POST'])
def test_create_user():
    """Create a test user for testing purposes"""
    try:
        # Check if test user already exists
        test_email = '<EMAIL>'
        existing_user = User.query.filter_by(email=test_email).first()
        
        if existing_user:
            return jsonify({
                'success': True,
                'message': 'Test user already exists',
                'user': {
                    'id': existing_user.id,
                    'email': existing_user.email,
                    'first_name': existing_user.first_name,
                    'last_name': existing_user.last_name
                }
            }), 200
        
        # Create test user
        test_user = User(
            first_name='Test',
            last_name='User',
            email=test_email,
            password='testpassword123'
        )
        
        db.session.add(test_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Test user created successfully',
            'user': {
                'id': test_user.id,
                'email': test_user.email,
                'first_name': test_user.first_name,
                'last_name': test_user.last_name
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Failed to create test user: {str(e)}'
        }), 500

@test_auth_bp.route('/test-system-status', methods=['GET'])
def test_system_status():
    """Get overall system status"""
    try:
        # Check various system components
        status = {
            'database': 'unknown',
            'models': 'unknown',
            'services': 'unknown'
        }
        
        # Test database
        try:
            db.session.execute('SELECT 1')
            status['database'] = 'healthy'
        except Exception:
            status['database'] = 'error'
        
        # Test models
        try:
            User.query.first()
            Resume.query.first()
            JobDescription.query.first()
            status['models'] = 'healthy'
        except Exception:
            status['models'] = 'error'
        
        # Test services
        try:
            from services.keyword_parser import KeywordParser
            from services.matching_service import MatchingService
            status['services'] = 'healthy'
        except Exception:
            status['services'] = 'error'
        
        overall_status = 'healthy' if all(s == 'healthy' for s in status.values()) else 'degraded'
        
        return jsonify({
            'success': True,
            'overall_status': overall_status,
            'components': status,
            'timestamp': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'System status check failed: {str(e)}'
        }), 500
