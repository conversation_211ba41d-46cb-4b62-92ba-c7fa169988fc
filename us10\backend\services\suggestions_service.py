#!/usr/bin/env python3
"""
Basic Suggestions Service for Dr. Resume
Provides basic resume improvement suggestions based on keyword analysis
"""

from services.dynamic_suggestions_service import DynamicSuggestionsService

class SuggestionsService:
    """Basic suggestions service using dynamic keyword analysis"""
    
    def __init__(self):
        self.dynamic_service = DynamicSuggestionsService()
    
    def generate_basic_suggestions(self, resume_id, job_description_id, user_id):
        """Generate basic suggestions for resume improvement"""
        return self.dynamic_service.generate_basic_suggestions(
            resume_id, job_description_id, user_id
        )
    
    def get_suggestion_history(self, user_id, limit=10):
        """Get suggestion history for a user"""
        # For now, return empty history as we don't store suggestions in DB
        return []
    
    def analyze_keywords_advanced(self, resume_id, job_description_id, user_id):
        """Advanced keyword analysis"""
        return self.dynamic_service.analyze_keywords_advanced(
            resume_id, job_description_id, user_id
        )
