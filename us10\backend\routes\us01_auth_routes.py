from flask import Blueprint, request, jsonify
from models import db, User
from flask_jwt_extended import create_access_token
import re

# Create blueprint for authentication routes
auth_bp = Blueprint('auth', __name__, url_prefix='/api')

@auth_bp.route('/register', methods=['POST'])
def register():
    """
    Register a new user
    Expected JSON payload:
    {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>", 
        "email": "<EMAIL>",
        "password": "securepassword",
        "confirm_password": "securepassword"
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract fields
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        confirm_password = data.get('confirm_password', '')
        
        # Validation
        errors = []
        
        # Check required fields
        if not first_name:
            errors.append('First name is required')
        if not last_name:
            errors.append('Last name is required')
        if not email:
            errors.append('Email is required')
        if not password:
            errors.append('Password is required')
        if not confirm_password:
            errors.append('Password confirmation is required')
        
        # Validate email format
        if email and not User.validate_email(email):
            errors.append('Invalid email format')
        
        # Validate password
        if password:
            is_valid, message = User.validate_password(password)
            if not is_valid:
                errors.append(message)
        
        # Check password confirmation
        if password and confirm_password and password != confirm_password:
            errors.append('Passwords do not match')
        
        # Check if user already exists
        if email and User.query.filter_by(email=email).first():
            errors.append('Email already registered')
        
        # Return validation errors
        if errors:
            return jsonify({
                'success': False,
                'message': 'Validation failed',
                'errors': errors
            }), 400
        
        # Create new user
        new_user = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            password=password
        )
        
        # Save to database
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Account created successfully! Please sign in.',
            'user': new_user.to_dict()
        }), 201
        
    except Exception as e:
        # Rollback in case of error
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Registration failed. Please try again.',
            'error': str(e)
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """
    Login user
    Expected JSON payload:
    {
        "email": "<EMAIL>",
        "password": "securepassword"
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400

        email = data.get('email', '').strip().lower()
        password = data.get('password', '')

        # Validation
        if not email:
            return jsonify({
                'success': False,
                'message': 'Email is required'
            }), 400

        if not password:
            return jsonify({
                'success': False,
                'message': 'Password is required'
            }), 400

        # Find user
        user = User.query.filter_by(email=email).first()

        if not user:
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401

        # Check password
        if not user.check_password(password):
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401

        # Generate access token
        access_token = create_access_token(identity=user.id)

        # Update last login
        user.update_last_login()

        return jsonify({
            'success': True,
            'message': 'Login successful',
            'access_token': access_token,
            'user': {
                'id': user.id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email
            }
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Login failed. Please try again.',
            'error': str(e)
        }), 500

@auth_bp.route('/check-email', methods=['POST'])
def check_email():
    """Check if email is already registered"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()
        
        if not email:
            return jsonify({
                'success': False,
                'message': 'Email is required'
            }), 400
        
        user_exists = User.query.filter_by(email=email).first() is not None
        
        return jsonify({
            'success': True,
            'exists': user_exists
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Error checking email',
            'error': str(e)
        }), 500
