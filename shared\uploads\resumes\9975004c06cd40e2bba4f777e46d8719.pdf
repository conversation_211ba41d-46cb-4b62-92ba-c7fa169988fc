
<PERSON>
Software Engineer

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:
Senior Software Engineer at TechCorp (2020-2024)
- Developed web applications using Python, Flask, and React
- Implemented RESTful APIs and microservices architecture
- Worked with PostgreSQL, MongoDB, and Redis databases
- Used Docker and Kubernetes for containerization
- Collaborated with cross-functional teams using Agile methodology

EDUCATION:
Bachelor of Science in Computer Science
University of Technology (2014-2018)

SKILLS:
Technical Skills: Python, JavaScript, React, Flask, Django, Node.js, HTML, CSS, SQL, PostgreSQL, MongoDB, Redis, Docker, Kubernetes, Git, Jenkins, AWS, Linux
Soft Skills: Leadership, Communication, Problem Solving, Team Collaboration, Project Management, Critical Thinking, Adaptability
